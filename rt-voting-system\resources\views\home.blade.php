@extends('layout')

@section('title', 'Beranda - Sistem Pemilihan Ketua RT')

@section('content')
<!-- Hero Section -->
<section class="hero-section">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h1 class="display-4 fw-bold mb-4">
                    <i class="bi bi-vote-fill"></i> Pemilihan Ketua RT
                </h1>
                <p class="lead mb-4">
                    {{ $config->nama_event ?? 'Pemilihan Ketua RT' }}
                </p>
                <p class="mb-4">
                    {{ $config->deskripsi ?? 'Sistem pemilihan elektronik yang aman dan transparan untuk memilih ketua RT periode mendatang.' }}
                </p>
                
                @if($config && $config->isVotingActive())
                    <div class="alert alert-success d-inline-block">
                        <i class="bi bi-clock"></i> 
                        <strong>Pemilihan Sedang Berlangsung!</strong><br>
                        Berakhir: {{ $config->waktu_selesai->format('d M Y, H:i') }}
                    </div>
                @else
                    <div class="alert alert-warning d-inline-block">
                        <i class="bi bi-clock"></i> 
                        <strong>Pemilihan Belum/Sudah Berakhir</strong>
                    </div>
                @endif
            </div>
            <div class="col-lg-4 text-center">
                <i class="bi bi-people-fill" style="font-size: 8rem; opacity: 0.3;"></i>
            </div>
        </div>
    </div>
</section>

<!-- Action Section -->
<section class="py-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8 text-center">
                @auth
                    @if(Auth::user()->pemilih && !Auth::user()->pemilih->hasVoted() && $config && $config->isVotingActive())
                        <h3 class="mb-4">Siap untuk Memberikan Suara?</h3>
                        <a href="{{ route('voting') }}" class="btn btn-vote btn-lg px-5 py-3">
                            <i class="bi bi-check-square"></i> Mulai Voting
                        </a>
                    @elseif(Auth::user()->pemilih && Auth::user()->pemilih->hasVoted())
                        <div class="alert alert-info">
                            <i class="bi bi-check-circle"></i> 
                            <strong>Terima kasih!</strong> Anda sudah memberikan suara.
                        </div>
                        <a href="{{ route('result') }}" class="btn btn-primary">
                            <i class="bi bi-bar-chart"></i> Lihat Hasil
                        </a>
                    @else
                        <div class="alert alert-warning">
                            <i class="bi bi-exclamation-triangle"></i> 
                            Pemilihan sedang tidak aktif atau Anda tidak terdaftar sebagai pemilih.
                        </div>
                    @endif
                @else
                    <h3 class="mb-4">Masuk untuk Memberikan Suara</h3>
                    <a href="{{ route('login') }}" class="btn btn-primary btn-lg px-5 py-3">
                        <i class="bi bi-box-arrow-in-right"></i> Login
                    </a>
                @endauth
            </div>
        </div>
    </div>
</section>

<!-- Candidates Section -->
<section class="py-5 bg-light">
    <div class="container">
        <h2 class="text-center mb-5">
            <i class="bi bi-people"></i> Calon Ketua RT
        </h2>
        
        <div class="row">
            @forelse($calons as $calon)
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card card-calon h-100">
                        <div class="card-body text-center">
                            <div class="mb-3">
                                <div class="bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center" 
                                     style="width: 80px; height: 80px; font-size: 2rem; font-weight: bold;">
                                    {{ $calon->nomor_urut }}
                                </div>
                            </div>
                            <h5 class="card-title">{{ $calon->nama }}</h5>
                            <p class="card-text">
                                <strong>Visi:</strong><br>
                                <small class="text-muted">{{ Str::limit($calon->visi, 100) }}</small>
                            </p>
                            <p class="card-text">
                                <strong>Misi:</strong><br>
                                <small class="text-muted">{{ Str::limit($calon->misi, 100) }}</small>
                            </p>
                        </div>
                    </div>
                </div>
            @empty
                <div class="col-12 text-center">
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle"></i> Belum ada calon yang terdaftar.
                    </div>
                </div>
            @endforelse
        </div>
    </div>
</section>

<!-- Statistics Section -->
<section class="py-5">
    <div class="container">
        <div class="row text-center">
            <div class="col-md-4">
                <div class="card border-0">
                    <div class="card-body">
                        <i class="bi bi-people-fill text-primary" style="font-size: 3rem;"></i>
                        <h3 class="mt-3">{{ $calons->count() }}</h3>
                        <p class="text-muted">Calon Terdaftar</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card border-0">
                    <div class="card-body">
                        <i class="bi bi-check-square-fill text-success" style="font-size: 3rem;"></i>
                        <h3 class="mt-3">{{ \App\Models\Suara::count() }}</h3>
                        <p class="text-muted">Suara Masuk</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card border-0">
                    <div class="card-body">
                        <i class="bi bi-person-check-fill text-info" style="font-size: 3rem;"></i>
                        <h3 class="mt-3">{{ \App\Models\Pemilih::count() }}</h3>
                        <p class="text-muted">Pemilih Terdaftar</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection
