<?php

namespace Symfony\Component\ErrorHandler\Tests\Fixtures;

/**
 * @final since version 3.3.
 */
class FinalClass1
{
    // simple comment
}

/**
 * @final
 */
class FinalClass2
{
    // no comment
}

/**
 * @final comment with @@@ and ***
 *
 * <AUTHOR>
 */
class FinalClass3
{
    // with comment and a tag after
}

/**
 * @final
 *
 * <AUTHOR>
 */
class FinalClass4
{
    // without comment and a tag after
}

/**
 * <AUTHOR>
 *
 * @final multiline
 * comment
 */
class FinalClass5
{
    // with comment and a tag before
}

/**
 * <AUTHOR>
 *
 * @final
 */
class FinalClass6
{
    // without comment and a tag before
}

/**
 * <AUTHOR>
 *
 * @final another
 *        multiline comment...
 *
 * @return string
 */
class FinalClass7
{
    // with comment and a tag before and after
}

/**
 * <AUTHOR>
 * @final
 *
 * @return string
 */
class FinalClass8
{
    // without comment and a tag before and after
}
