<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Calon extends Model
{
    use HasFactory;

    protected $fillable = [
        'nama',
        'foto',
        'visi',
        'misi',
        'rekam_jejak',
        'nomor_urut',
        'created_by',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * Get the user who created this calon.
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get all suaras for this calon.
     */
    public function suaras()
    {
        return $this->hasMany(Suara::class);
    }

    /**
     * Get total votes count
     */
    public function getTotalVotesAttribute(): int
    {
        return $this->suaras()->count();
    }

    /**
     * Scope for active calons
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Get foto URL
     */
    public function getFotoUrlAttribute(): ?string
    {
        return $this->foto ? asset('storage/' . $this->foto) : null;
    }
}
