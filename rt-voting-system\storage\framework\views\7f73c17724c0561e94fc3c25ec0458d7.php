<?php $__env->startSection('title', 'Dashboard Admin'); ?>
<?php $__env->startSection('page-title', 'Dashboard'); ?>

<?php $__env->startSection('content'); ?>
<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card stats-card">
            <div class="card-body text-center">
                <i class="bi bi-people-fill" style="font-size: 2.5rem;"></i>
                <h3 class="mt-2"><?php echo e($stats['total_pemilih']); ?></h3>
                <p class="mb-0">Total Pemilih</p>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <i class="bi bi-check-square-fill" style="font-size: 2.5rem;"></i>
                <h3 class="mt-2"><?php echo e($stats['total_suara']); ?></h3>
                <p class="mb-0">Suara Masuk</p>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <i class="bi bi-person-check-fill" style="font-size: 2.5rem;"></i>
                <h3 class="mt-2"><?php echo e($stats['pemilih_sudah_voting']); ?></h3>
                <p class="mb-0">Sudah Voting</p>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-warning text-white">
            <div class="card-body text-center">
                <i class="bi bi-person-x-fill" style="font-size: 2.5rem;"></i>
                <h3 class="mt-2"><?php echo e($stats['total_pemilih'] - $stats['pemilih_sudah_voting']); ?></h3>
                <p class="mb-0">Belum Voting</p>
            </div>
        </div>
    </div>
</div>

<!-- Participation Rate -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-graph-up"></i> Tingkat Partisipasi
                </h5>
            </div>
            <div class="card-body">
                <?php
                    $participation = $stats['total_pemilih'] > 0 ? ($stats['pemilih_sudah_voting'] / $stats['total_pemilih']) * 100 : 0;
                ?>
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <div class="progress" style="height: 30px;">
                            <div class="progress-bar bg-success" 
                                 role="progressbar" 
                                 style="width: <?php echo e($participation); ?>%"
                                 aria-valuenow="<?php echo e($participation); ?>" 
                                 aria-valuemin="0" 
                                 aria-valuemax="100">
                                <strong><?php echo e(number_format($participation, 1)); ?>%</strong>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 text-center">
                        <h4 class="text-success mb-0"><?php echo e(number_format($participation, 1)); ?>%</h4>
                        <small class="text-muted">Partisipasi Pemilih</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-lightning"></i> Aksi Cepat
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-2">
                        <a href="<?php echo e(route('admin.pemilih.create')); ?>" class="btn btn-primary w-100">
                            <i class="bi bi-person-plus"></i> Tambah Pemilih
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="<?php echo e(route('admin.pemilih.index')); ?>" class="btn btn-info w-100">
                            <i class="bi bi-people"></i> Kelola Pemilih
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="<?php echo e(route('result')); ?>" class="btn btn-success w-100">
                            <i class="bi bi-bar-chart"></i> Lihat Hasil
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="<?php echo e(route('home')); ?>" class="btn btn-outline-secondary w-100">
                            <i class="bi bi-house"></i> Ke Beranda
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Votes -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="bi bi-clock-history"></i> Suara Terbaru
                </h5>
                <small class="text-muted">10 suara terakhir</small>
            </div>
            <div class="card-body">
                <?php if($recentVotes->count() > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Waktu</th>
                                    <th>Pemilih</th>
                                    <th>NIK</th>
                                    <th>Pilihan</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $recentVotes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $vote): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td>
                                            <small><?php echo e($vote->waktu->format('d/m/Y H:i')); ?></small>
                                        </td>
                                        <td><?php echo e($vote->pemilih->nama_lengkap); ?></td>
                                        <td>
                                            <small class="text-muted"><?php echo e($vote->pemilih->nik); ?></small>
                                        </td>
                                        <td>
                                            <span class="badge bg-primary">
                                                <?php echo e($vote->calon->nomor_urut); ?>. <?php echo e($vote->calon->nama); ?>

                                            </span>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="text-center text-muted py-4">
                        <i class="bi bi-inbox" style="font-size: 3rem;"></i>
                        <p class="mt-2">Belum ada suara yang masuk</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
// Auto refresh every 30 seconds
setTimeout(function() {
    location.reload();
}, 30000);

// Add animation to progress bar
document.addEventListener('DOMContentLoaded', function() {
    const progressBar = document.querySelector('.progress-bar');
    if (progressBar) {
        const width = progressBar.style.width;
        progressBar.style.width = '0%';
        setTimeout(() => {
            progressBar.style.transition = 'width 1s ease-in-out';
            progressBar.style.width = width;
        }, 100);
    }
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\pemilihan RT\rt\rt-voting-system\resources\views/admin/dashboard.blade.php ENDPATH**/ ?>