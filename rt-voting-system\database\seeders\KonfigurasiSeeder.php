<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Konfigurasi;

class KonfigurasiSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        Konfigurasi::create([
            'nama_event' => 'Pemilihan Ketua RT 01/RW 01 Periode 2024-2027',
            'deskripsi' => 'Pemilihan Ketua RT untuk periode 2024-2027 dengan sistem voting elektronik yang aman dan transparan.',
            'waktu_mulai' => now(),
            'waktu_selesai' => now()->addDays(7), // Voting berlangsung 7 hari
            'is_active' => true,
            'pengaturan_tambahan' => [
                'max_votes_per_pemilih' => 1,
                'show_real_time_results' => false,
                'require_photo_verification' => false,
                'allow_vote_change' => false,
            ],
        ]);
    }
}
