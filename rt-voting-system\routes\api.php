<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\CalonController;
use App\Http\Controllers\Api\PemilihController;
use App\Http\Controllers\Api\VotingController;

// Public routes
Route::post('/login', [AuthController::class, 'login']);
Route::get('/calons', [CalonController::class, 'index']);
Route::get('/calons/{calon}', [CalonController::class, 'show']);

// Protected routes
Route::middleware('auth:sanctum')->group(function () {
    Route::get('/user', function (Request $request) {
        return $request->user();
    });
    
    Route::post('/logout', [AuthController::class, 'logout']);
    
    // Voting routes
    Route::post('/vote', [VotingController::class, 'vote']);
    Route::get('/my-vote', [VotingController::class, 'myVote']);
    Route::get('/voting-status', [VotingController::class, 'votingStatus']);
    
    // Admin routes
    Route::middleware('admin')->group(function () {
        Route::apiResource('calons', CalonController::class)->except(['index', 'show']);
        Route::apiResource('pemilihs', PemilihController::class);
        Route::get('/results', [VotingController::class, 'results']);
        Route::get('/dashboard-stats', [VotingController::class, 'dashboardStats']);
    });
});
