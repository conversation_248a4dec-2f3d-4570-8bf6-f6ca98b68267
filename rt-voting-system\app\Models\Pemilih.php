<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Pemilih extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'nik',
        'nama_lengkap',
        'alamat',
        'tempat_lahir',
        'tanggal_lahir',
        'jenis_kelamin',
        'peker<PERSON>an',
        'no_hp',
        'status_memilih',
        'waktu_memilih',
    ];

    protected $casts = [
        'waktu_memilih' => 'datetime',
        'tanggal_lahir' => 'date',
    ];

    /**
     * Get the user that owns the pemilih.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the suara for this pemilih.
     */
    public function suara()
    {
        return $this->hasOne(Suara::class);
    }

    /**
     * Check if pemilih has voted
     */
    public function hasVoted(): bool
    {
        return $this->status_memilih === 'sudah';
    }

    /**
     * Mark as voted
     */
    public function markAsVoted()
    {
        $this->update([
            'status_memilih' => 'sudah',
            'waktu_memilih' => now(),
        ]);
    }
}
