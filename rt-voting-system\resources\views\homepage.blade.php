@extends('layout')

@section('title', 'Beranda - Sistem Pemilihan Ketua RT')

@section('content')
<!-- Hero Section with Enhanced Design -->
<section class="hero-section position-relative overflow-hidden">
    <div class="container">
        <div class="row align-items-center min-vh-75">
            <div class="col-lg-7">
                <div class="badge bg-primary bg-opacity-10 text-primary px-3 py-2 rounded-pill mb-3">
                    <i class="bi bi-shield-check me-1"></i> Sistem Voting Digital
                </div>
                <h1 class="display-3 fw-bold mb-4 text-gradient">
                    Pemilihan Ketua RT
                </h1>
                <p class="lead mb-4 text-muted">
                    {{ $config->nama_event ?? 'Pemilihan Ketua RT Periode 2024-2027' }}
                </p>
                <p class="fs-5 mb-4 text-muted">
                    {{ $config->deskripsi ?? 'Sistem pemilihan elektronik yang aman dan transparan untuk memilih ketua RT periode mendatang. Setiap warga yang terdaftar dapat memberikan suara secara online.' }}
                </p>

                @if($config && $config->isVotingActive())
                    <div class="alert alert-success border-0 shadow-sm d-inline-flex align-items-center">
                        <div class="spinner-grow spinner-grow-sm text-success me-3" role="status"></div>
                        <div>
                            <strong><i class="bi bi-broadcast"></i> Pemilihan Sedang Berlangsung!</strong><br>
                            <small>Berakhir: {{ $config->waktu_selesai->format('d M Y, H:i') }}</small>
                        </div>
                    </div>
                @else
                    <div class="alert alert-warning border-0 shadow-sm d-inline-flex align-items-center">
                        <i class="bi bi-clock-history fs-4 me-3"></i>
                        <div>
                            <strong>Pemilihan Belum/Sudah Berakhir</strong><br>
                            <small>Silakan pantau pengumuman selanjutnya</small>
                        </div>
                    </div>
                @endif
            </div>
            <div class="col-lg-5 text-center">
                <div class="position-relative">
                    <div class="bg-primary bg-opacity-10 rounded-circle p-5 d-inline-block">
                        <i class="bi bi-people-fill text-primary" style="font-size: 8rem;"></i>
                    </div>
                    <div class="position-absolute top-0 start-0 w-100 h-100">
                        <div class="floating-icons">
                            <i class="bi bi-check-circle-fill text-success position-absolute" style="top: 20%; left: 10%; font-size: 2rem; animation: float 3s ease-in-out infinite;"></i>
                            <i class="bi bi-shield-check text-primary position-absolute" style="top: 60%; right: 15%; font-size: 1.5rem; animation: float 3s ease-in-out infinite 1s;"></i>
                            <i class="bi bi-graph-up text-info position-absolute" style="bottom: 20%; left: 20%; font-size: 1.8rem; animation: float 3s ease-in-out infinite 2s;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Background decoration -->
    <div class="position-absolute top-0 end-0 translate-middle-y">
        <div class="bg-primary bg-opacity-5 rounded-circle" style="width: 300px; height: 300px;"></div>
    </div>
</section>

<!-- Enhanced Action Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                @auth
                    @if(Auth::user()->isAdmin())
                        <div class="card border-0 shadow-lg bg-gradient-warning">
                            <div class="card-body text-center p-5">
                                <div class="mb-4">
                                    <div class="bg-white bg-opacity-20 rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 80px; height: 80px;">
                                        <i class="bi bi-shield-check text-white fs-1"></i>
                                    </div>
                                </div>
                                <h2 class="text-white mb-3">Selamat Datang, Administrator!</h2>
                                <p class="text-white mb-4 fs-5">Kelola sistem pemilihan melalui dashboard admin yang lengkap dan powerful.</p>
                                <div class="d-flex flex-wrap gap-3 justify-content-center">
                                    <a href="{{ route('admin.dashboard') }}" class="btn btn-light btn-lg px-4 py-3 shadow">
                                        <i class="bi bi-speedometer2 me-2"></i> Dashboard Admin
                                    </a>
                                    <a href="{{ route('result') }}" class="btn btn-outline-light btn-lg px-4 py-3">
                                        <i class="bi bi-bar-chart me-2"></i> Lihat Hasil
                                    </a>
                                </div>
                            </div>
                        </div>
                    @elseif(Auth::user()->pemilih && !Auth::user()->pemilih->hasVoted() && $config && $config->isVotingActive())
                        <div class="card border-0 shadow-lg bg-gradient-success">
                            <div class="card-body text-center p-5">
                                <div class="mb-4">
                                    <div class="bg-white bg-opacity-20 rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 80px; height: 80px;">
                                        <i class="bi bi-check-square text-white fs-1"></i>
                                    </div>
                                </div>
                                <h2 class="text-white mb-3">Siap untuk Memberikan Suara?</h2>
                                <p class="text-white mb-4 fs-5">Suara Anda sangat berharga untuk masa depan RT yang lebih baik.</p>
                                <div class="d-flex flex-wrap gap-3 justify-content-center">
                                    <a href="{{ route('voting') }}" class="btn btn-light btn-lg px-5 py-3 shadow pulse-button">
                                        <i class="bi bi-check-square me-2"></i> Mulai Voting
                                    </a>
                                    <a href="{{ route('result') }}" class="btn btn-outline-light btn-lg px-4 py-3">
                                        <i class="bi bi-bar-chart me-2"></i> Lihat Hasil
                                    </a>
                                </div>
                            </div>
                        </div>
                    @elseif(Auth::user()->pemilih && Auth::user()->pemilih->hasVoted())
                        <div class="card border-0 shadow-lg bg-gradient-info">
                            <div class="card-body text-center p-5">
                                <div class="mb-4">
                                    <div class="bg-white bg-opacity-20 rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 80px; height: 80px;">
                                        <i class="bi bi-check-circle text-white fs-1"></i>
                                    </div>
                                </div>
                                <h2 class="text-white mb-3">Terima Kasih!</h2>
                                <p class="text-white mb-4 fs-5">Anda sudah memberikan suara. Pantau hasil pemilihan secara real-time.</p>
                                <a href="{{ route('result') }}" class="btn btn-light btn-lg px-5 py-3 shadow">
                                    <i class="bi bi-bar-chart me-2"></i> Lihat Hasil Pemilihan
                                </a>
                            </div>
                        </div>
                    @else
                        <div class="card border-0 shadow-lg bg-gradient-secondary">
                            <div class="card-body text-center p-5">
                                <div class="mb-4">
                                    <div class="bg-white bg-opacity-20 rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 80px; height: 80px;">
                                        <i class="bi bi-exclamation-triangle text-white fs-1"></i>
                                    </div>
                                </div>
                                <h2 class="text-white mb-3">Pemilihan Tidak Aktif</h2>
                                <p class="text-white mb-4 fs-5">Pemilihan sedang tidak aktif atau Anda tidak terdaftar sebagai pemilih.</p>
                                <a href="{{ route('result') }}" class="btn btn-light btn-lg px-5 py-3 shadow">
                                    <i class="bi bi-bar-chart me-2"></i> Lihat Hasil
                                </a>
                            </div>
                        </div>
                    @endif
                @else
                    <div class="card border-0 shadow-lg bg-gradient-primary">
                        <div class="card-body text-center p-5">
                            <div class="mb-4">
                                <div class="bg-white bg-opacity-20 rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 80px; height: 80px;">
                                    <i class="bi bi-box-arrow-in-right text-white fs-1"></i>
                                </div>
                            </div>
                            <h2 class="text-white mb-3">Masuk untuk Memberikan Suara</h2>
                            <p class="text-white mb-4 fs-5">Login dengan akun yang telah terdaftar untuk berpartisipasi dalam pemilihan.</p>
                            <div class="d-flex flex-wrap gap-3 justify-content-center">
                                <a href="{{ route('login') }}" class="btn btn-light btn-lg px-5 py-3 shadow">
                                    <i class="bi bi-box-arrow-in-right me-2"></i> Login Sekarang
                                </a>
                                <a href="{{ route('result') }}" class="btn btn-outline-light btn-lg px-4 py-3">
                                    <i class="bi bi-bar-chart me-2"></i> Lihat Hasil
                                </a>
                            </div>
                        </div>
                    </div>
                @endauth
            </div>
        </div>
    </div>
</section>

<!-- Enhanced Statistics Section -->
<section class="py-5">
    <div class="container">
        <div class="text-center mb-5">
            <div class="badge bg-primary bg-opacity-10 text-primary px-3 py-2 rounded-pill mb-3">
                <i class="bi bi-graph-up me-1"></i> Real-time Data
            </div>
            <h2 class="display-5 fw-bold mb-3">Statistik Pemilihan</h2>
            <p class="text-muted fs-5">Data terkini dari sistem pemilihan elektronik</p>
        </div>

        <div class="row g-4">
            <div class="col-lg-3 col-md-6">
                <div class="card border-0 shadow-sm h-100 hover-lift">
                    <div class="card-body text-center p-4">
                        <div class="bg-primary bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 70px; height: 70px;">
                            <i class="bi bi-people-fill text-primary fs-2"></i>
                        </div>
                        <h3 class="fw-bold text-primary mb-2">{{ $calons->count() }}</h3>
                        <p class="text-muted mb-0">Calon Terdaftar</p>
                        <small class="text-success">
                            <i class="bi bi-check-circle"></i> Terverifikasi
                        </small>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="card border-0 shadow-sm h-100 hover-lift">
                    <div class="card-body text-center p-4">
                        <div class="bg-success bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 70px; height: 70px;">
                            <i class="bi bi-check-square-fill text-success fs-2"></i>
                        </div>
                        <h3 class="fw-bold text-success mb-2 counter" data-target="{{ $totalVotes }}">0</h3>
                        <p class="text-muted mb-0">Suara Masuk</p>
                        <small class="text-success">
                            <i class="bi bi-arrow-up"></i> Live Count
                        </small>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="card border-0 shadow-sm h-100 hover-lift">
                    <div class="card-body text-center p-4">
                        <div class="bg-info bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 70px; height: 70px;">
                            <i class="bi bi-person-check-fill text-info fs-2"></i>
                        </div>
                        <h3 class="fw-bold text-info mb-2">{{ $totalPemilih }}</h3>
                        <p class="text-muted mb-0">Pemilih Terdaftar</p>
                        <small class="text-info">
                            <i class="bi bi-shield-check"></i> DPT Valid
                        </small>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="card border-0 shadow-sm h-100 hover-lift">
                    <div class="card-body text-center p-4">
                        <div class="bg-warning bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 70px; height: 70px;">
                            <i class="bi bi-percent text-warning fs-2"></i>
                        </div>
                        @php
                            $participation = $totalPemilih > 0 ? ($totalVotes / $totalPemilih) * 100 : 0;
                        @endphp
                        <h3 class="fw-bold text-warning mb-2">{{ number_format($participation, 1) }}%</h3>
                        <p class="text-muted mb-0">Partisipasi</p>
                        <div class="progress mt-2" style="height: 6px;">
                            <div class="progress-bar bg-warning" role="progressbar" style="width: {{ $participation }}%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Additional Stats Row -->
        <div class="row mt-5">
            <div class="col-12">
                <div class="card border-0 shadow-sm bg-gradient-primary text-white">
                    <div class="card-body p-4">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <h5 class="mb-2">
                                    <i class="bi bi-clock-history me-2"></i>
                                    Status Pemilihan
                                </h5>
                                @if($config && $config->isVotingActive())
                                    <p class="mb-0 fs-6">
                                        <span class="badge bg-success me-2">AKTIF</span>
                                        Pemilihan berlangsung hingga {{ $config->waktu_selesai->format('d M Y, H:i') }}
                                    </p>
                                @else
                                    <p class="mb-0 fs-6">
                                        <span class="badge bg-warning me-2">TIDAK AKTIF</span>
                                        Pemilihan sedang tidak berlangsung
                                    </p>
                                @endif
                            </div>
                            <div class="col-md-4 text-md-end">
                                <div class="d-flex align-items-center justify-content-md-end">
                                    <div class="me-3">
                                        <small class="d-block">Sistem</small>
                                        <span class="badge bg-success">Online</span>
                                    </div>
                                    <div class="bg-white bg-opacity-20 rounded-circle p-2">
                                        <i class="bi bi-shield-check fs-4"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Candidates Section -->
<section class="py-5">
    <div class="container">
        <h2 class="text-center mb-5">
            <i class="bi bi-people"></i> Calon Ketua RT
        </h2>

        <div class="row">
            @forelse($calons as $calon)
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card card-calon h-100">
                        <div class="card-body text-center">
                            <div class="mb-3">
                                <div class="bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center"
                                     style="width: 80px; height: 80px; font-size: 2rem; font-weight: bold;">
                                    {{ $calon->nomor_urut }}
                                </div>
                            </div>
                            <h5 class="card-title">{{ $calon->nama }}</h5>
                            <p class="card-text">
                                <strong>Visi:</strong><br>
                                <small class="text-muted">{{ Str::limit($calon->visi, 100) }}</small>
                            </p>
                            <p class="card-text">
                                <strong>Misi:</strong><br>
                                <small class="text-muted">{{ Str::limit($calon->misi, 100) }}</small>
                            </p>
                            @if($calon->rekam_jejak)
                                <p class="card-text">
                                    <strong>Rekam Jejak:</strong><br>
                                    <small class="text-muted">{{ Str::limit($calon->rekam_jejak, 80) }}</small>
                                </p>
                            @endif
                        </div>
                    </div>
                </div>
            @empty
                <div class="col-12 text-center">
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle"></i> Belum ada calon yang terdaftar.
                    </div>
                </div>
            @endforelse
        </div>
    </div>
</section>

<!-- Information Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-md-6 mb-4">
                <div class="card h-100">
                    <div class="card-body">
                        <h5 class="card-title">
                            <i class="bi bi-info-circle text-primary"></i> Cara Voting
                        </h5>
                        <ol class="mb-0">
                            <li>Login dengan akun yang telah terdaftar</li>
                            <li>Pilih salah satu calon yang tersedia</li>
                            <li>Konfirmasi pilihan Anda</li>
                            <li>Suara akan tersimpan dan tidak dapat diubah</li>
                        </ol>
                    </div>
                </div>
            </div>
            <div class="col-md-6 mb-4">
                <div class="card h-100">
                    <div class="card-body">
                        <h5 class="card-title">
                            <i class="bi bi-shield-check text-success"></i> Keamanan
                        </h5>
                        <ul class="mb-0">
                            <li>Setiap pemilih hanya dapat memilih sekali</li>
                            <li>Data voting terenkripsi dan aman</li>
                            <li>Hasil dapat dipantau secara real-time</li>
                            <li>Sistem transparan dan dapat diaudit</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection
