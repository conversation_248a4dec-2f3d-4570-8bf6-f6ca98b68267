<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\WebController;
use Illuminate\Support\Facades\Auth;

Route::get('/', function() {
    if (Auth::check()) {
        $user = Auth::user();
        if ($user->isAdmin()) {
            return redirect()->route('admin.dashboard');
        } else {
            return redirect()->route('voting');
        }
    }
    return redirect()->route('login');
})->name('home');

Route::get('/login', [WebController::class, 'login'])->name('login');
Route::get('/voting', [WebController::class, 'voting'])->name('voting');
Route::get('/result', [WebController::class, 'result'])->name('result');

// Auth routes
Route::post('/login', function(\Illuminate\Http\Request $request) {
    $request->validate([
        'email' => 'required|email',
        'password' => 'required',
    ]);

    if (Auth::attempt($request->only('email', 'password'))) {
        $user = Auth::user();

        // Redirect based on user role
        if ($user->isAdmin()) {
            return redirect()->route('admin.dashboard');
        } else {
            return redirect()->route('voting');
        }
    }

    return back()->withErrors(['email' => 'Email atau password salah']);
})->name('login.post');

Route::post('/logout', function() {
    Auth::logout();
    return redirect()->route('login');
})->name('logout');

// Vote submission
Route::post('/vote', function(\Illuminate\Http\Request $request) {
    $request->validate([
        'calon_id' => 'required|exists:calons,id',
    ]);

    $user = Auth::user();
    $pemilih = $user->pemilih;

    if (!$pemilih || $pemilih->hasVoted()) {
        return back()->with('error', 'Tidak dapat memberikan suara');
    }

    \App\Models\Suara::create([
        'pemilih_id' => $pemilih->id,
        'calon_id' => $request->calon_id,
    ]);

    $pemilih->markAsVoted();

    return redirect()->route('result')->with('success', 'Suara berhasil disimpan!');
})->name('vote.submit')->middleware('auth');

// Admin routes
Route::middleware(['auth', 'admin'])->prefix('admin')->name('admin.')->group(function () {
    Route::get('/dashboard', [\App\Http\Controllers\AdminController::class, 'dashboard'])->name('dashboard');

    // Pemilih CRUD
    Route::get('/pemilih', [\App\Http\Controllers\AdminController::class, 'pemilihIndex'])->name('pemilih.index');
    Route::get('/pemilih/create', [\App\Http\Controllers\AdminController::class, 'pemilihCreate'])->name('pemilih.create');
    Route::post('/pemilih', [\App\Http\Controllers\AdminController::class, 'pemilihStore'])->name('pemilih.store');
    Route::get('/pemilih/{pemilih}/edit', [\App\Http\Controllers\AdminController::class, 'pemilihEdit'])->name('pemilih.edit');
    Route::put('/pemilih/{pemilih}', [\App\Http\Controllers\AdminController::class, 'pemilihUpdate'])->name('pemilih.update');
    Route::delete('/pemilih/{pemilih}', [\App\Http\Controllers\AdminController::class, 'pemilihDestroy'])->name('pemilih.destroy');
});
