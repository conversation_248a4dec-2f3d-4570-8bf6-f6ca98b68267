<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\WebController;
use Illuminate\Support\Facades\Auth;

Route::get('/', [WebController::class, 'home'])->name('home');
Route::get('/login', [WebController::class, 'login'])->name('login');
Route::get('/voting', [WebController::class, 'voting'])->name('voting');
Route::get('/result', [WebController::class, 'result'])->name('result');

// Auth routes
Route::post('/login', function(\Illuminate\Http\Request $request) {
    $request->validate([
        'email' => 'required|email',
        'password' => 'required',
    ]);

    if (Auth::attempt($request->only('email', 'password'))) {
        return redirect()->route('voting');
    }

    return back()->withErrors(['email' => 'Email atau password salah']);
})->name('login.post');

Route::post('/logout', function() {
    Auth::logout();
    return redirect()->route('home');
})->name('logout');

// Vote submission
Route::post('/vote', function(\Illuminate\Http\Request $request) {
    $request->validate([
        'calon_id' => 'required|exists:calons,id',
    ]);

    $user = Auth::user();
    $pemilih = $user->pemilih;

    if (!$pemilih || $pemilih->hasVoted()) {
        return back()->with('error', 'Tidak dapat memberikan suara');
    }

    \App\Models\Suara::create([
        'pemilih_id' => $pemilih->id,
        'calon_id' => $request->calon_id,
    ]);

    $pemilih->markAsVoted();

    return redirect()->route('result')->with('success', 'Suara berhasil disimpan!');
})->name('vote.submit')->middleware('auth');
