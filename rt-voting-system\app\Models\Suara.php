<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Suara extends Model
{
    use HasFactory;

    protected $fillable = [
        'pemilih_id',
        'calon_id',
        'waktu',
        'ip_address',
        'user_agent',
    ];

    protected $casts = [
        'waktu' => 'datetime',
    ];

    /**
     * Get the pemilih that owns the suara.
     */
    public function pemilih()
    {
        return $this->belongsTo(Pemilih::class);
    }

    /**
     * Get the calon that was voted for.
     */
    public function calon()
    {
        return $this->belongsTo(Calon::class);
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($suara) {
            $suara->waktu = now();
            $suara->ip_address = request()->ip();
            $suara->user_agent = request()->userAgent();
        });
    }
}
