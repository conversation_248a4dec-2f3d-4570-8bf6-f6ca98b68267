@extends('layout')

@section('title', 'Login - Sistem Pemilihan Ketua RT')

@section('content')
<section class="py-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-5">
                <div class="card shadow">
                    <div class="card-body p-5">
                        <div class="text-center mb-4">
                            <i class="bi bi-person-circle text-primary" style="font-size: 4rem;"></i>
                            <h3 class="mt-3">Login Pemilih</h3>
                            <p class="text-muted">Masuk untuk memberikan suara</p>
                        </div>

                        @if($errors->any())
                            <div class="alert alert-danger">
                                <i class="bi bi-exclamation-triangle"></i>
                                @foreach($errors->all() as $error)
                                    {{ $error }}
                                @endforeach
                            </div>
                        @endif

                        <form action="{{ route('login.post') }}" method="POST">
                            @csrf
                            <div class="mb-3">
                                <label for="email" class="form-label">
                                    <i class="bi bi-envelope"></i> Email
                                </label>
                                <input type="email"
                                       class="form-control @error('email') is-invalid @enderror"
                                       id="email"
                                       name="email"
                                       value="{{ old('email') }}"
                                       required
                                       autofocus>
                                @error('email')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="mb-4">
                                <label for="password" class="form-label">
                                    <i class="bi bi-lock"></i> Password
                                </label>
                                <input type="password"
                                       class="form-control @error('password') is-invalid @enderror"
                                       id="password"
                                       name="password"
                                       required>
                                @error('password')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="bi bi-box-arrow-in-right"></i> Login
                                </button>
                            </div>
                        </form>

                        <hr class="my-4">

                        <div class="text-center">
                            <p class="text-muted mb-3">Akun Demo untuk Testing:</p>
                            <div class="row">
                                <div class="col-12 mb-3">
                                    <div class="card bg-warning bg-opacity-10 border-warning">
                                        <div class="card-body py-2">
                                            <small class="text-dark">
                                                <strong><i class="bi bi-shield-check"></i> Admin:</strong><br>
                                                <code><EMAIL></code> / <code>password123</code><br>
                                                <em>→ Langsung ke Dashboard Admin</em>
                                            </small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <div class="card bg-primary bg-opacity-10 border-primary">
                                        <div class="card-body py-2">
                                            <small class="text-dark">
                                                <strong><i class="bi bi-person-check"></i> Pemilih:</strong><br>
                                                <code><EMAIL></code> / <code>password123</code><br>
                                                <em>→ Langsung ke Halaman Voting</em>
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>


                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection
