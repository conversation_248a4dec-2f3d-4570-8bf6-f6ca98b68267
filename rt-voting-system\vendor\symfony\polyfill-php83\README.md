Symfony Polyfill / Php83
========================

This component provides features added to PHP 8.3 core:

- [`json_validate`](https://wiki.php.net/rfc/json_validate)
- [`Override`](https://wiki.php.net/rfc/marking_overriden_methods)
- [`mb_str_pad`](https://wiki.php.net/rfc/mb_str_pad)
- [`ldap_exop_sync`](https://wiki.php.net/rfc/deprecate_functions_with_overloaded_signatures)
- [`ldap_connect_wallet`](https://wiki.php.net/rfc/deprecate_functions_with_overloaded_signatures)
- [`stream_context_set_options`](https://wiki.php.net/rfc/deprecate_functions_with_overloaded_signatures)
- [`str_increment` and `str_decrement`](https://wiki.php.net/rfc/saner-inc-dec-operators)
- [`Date*Exception/Error classes`](https://wiki.php.net/rfc/datetime-exceptions)
- [`SQLite3Exception`](https://wiki.php.net/rfc/sqlite3_exceptions)

More information can be found in the
[main Polyfill README](https://github.com/symfony/polyfill/blob/main/README.md).

License
=======

This library is released under the [MIT license](LICENSE).
