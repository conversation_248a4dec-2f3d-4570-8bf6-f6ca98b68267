<?php $__env->startSection('title', 'Beranda - Sistem Pemilihan Ketua RT'); ?>

<?php $__env->startSection('content'); ?>
<!-- Hero Section -->
<section class="hero-section">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h1 class="display-4 fw-bold mb-4">
                    <i class="bi bi-vote-fill"></i> Pemilihan Ketua RT
                </h1>
                <p class="lead mb-4">
                    <?php echo e($config->nama_event ?? 'Pemilihan Ketua RT'); ?>

                </p>
                <p class="mb-4">
                    <?php echo e($config->deskripsi ?? 'Sistem pemilihan elektronik yang aman dan transparan untuk memilih ketua RT periode mendatang.'); ?>

                </p>

                <?php if($config && $config->isVotingActive()): ?>
                    <div class="alert alert-success d-inline-block">
                        <i class="bi bi-clock"></i>
                        <strong>Pemilihan Sedang Berlangsung!</strong><br>
                        Berakhir: <?php echo e($config->waktu_selesai->format('d M Y, H:i')); ?>

                    </div>
                <?php else: ?>
                    <div class="alert alert-warning d-inline-block">
                        <i class="bi bi-clock"></i>
                        <strong>Pemilihan Belum/Sudah Berakhir</strong>
                    </div>
                <?php endif; ?>
            </div>
            <div class="col-lg-4 text-center">
                <i class="bi bi-people-fill" style="font-size: 8rem; opacity: 0.3;"></i>
            </div>
        </div>
    </div>
</section>

<!-- Action Section -->
<section class="py-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8 text-center">
                <?php if(auth()->guard()->check()): ?>
                    <?php if(Auth::user()->isAdmin()): ?>
                        <h3 class="mb-4">Selamat Datang, Administrator!</h3>
                        <div class="alert alert-warning">
                            <i class="bi bi-shield-check"></i>
                            <strong>Admin Panel:</strong> Kelola sistem pemilihan melalui dashboard admin.
                        </div>
                        <a href="<?php echo e(route('admin.dashboard')); ?>" class="btn btn-warning btn-lg px-5 py-3">
                            <i class="bi bi-speedometer2"></i> Buka Admin Panel
                        </a>
                    <?php elseif(Auth::user()->pemilih && !Auth::user()->pemilih->hasVoted() && $config && $config->isVotingActive()): ?>
                        <h3 class="mb-4">Siap untuk Memberikan Suara?</h3>
                        <a href="<?php echo e(route('voting')); ?>" class="btn btn-vote btn-lg px-5 py-3">
                            <i class="bi bi-check-square"></i> Mulai Voting
                        </a>
                    <?php elseif(Auth::user()->pemilih && Auth::user()->pemilih->hasVoted()): ?>
                        <div class="alert alert-info">
                            <i class="bi bi-check-circle"></i>
                            <strong>Terima kasih!</strong> Anda sudah memberikan suara.
                        </div>
                        <a href="<?php echo e(route('result')); ?>" class="btn btn-primary">
                            <i class="bi bi-bar-chart"></i> Lihat Hasil
                        </a>
                    <?php else: ?>
                        <div class="alert alert-warning">
                            <i class="bi bi-exclamation-triangle"></i>
                            Pemilihan sedang tidak aktif atau Anda tidak terdaftar sebagai pemilih.
                        </div>
                    <?php endif; ?>
                <?php else: ?>
                    <h3 class="mb-4">Masuk untuk Memberikan Suara</h3>
                    <a href="<?php echo e(route('login')); ?>" class="btn btn-primary btn-lg px-5 py-3">
                        <i class="bi bi-box-arrow-in-right"></i> Login
                    </a>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>

<!-- Candidates Section -->
<section class="py-5 bg-light">
    <div class="container">
        <h2 class="text-center mb-5">
            <i class="bi bi-people"></i> Calon Ketua RT
        </h2>

        <div class="row">
            <?php $__empty_1 = true; $__currentLoopData = $calons; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $calon): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card card-calon h-100">
                        <div class="card-body text-center">
                            <div class="mb-3">
                                <div class="bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center"
                                     style="width: 80px; height: 80px; font-size: 2rem; font-weight: bold;">
                                    <?php echo e($calon->nomor_urut); ?>

                                </div>
                            </div>
                            <h5 class="card-title"><?php echo e($calon->nama); ?></h5>
                            <p class="card-text">
                                <strong>Visi:</strong><br>
                                <small class="text-muted"><?php echo e(Str::limit($calon->visi, 100)); ?></small>
                            </p>
                            <p class="card-text">
                                <strong>Misi:</strong><br>
                                <small class="text-muted"><?php echo e(Str::limit($calon->misi, 100)); ?></small>
                            </p>
                        </div>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <div class="col-12 text-center">
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle"></i> Belum ada calon yang terdaftar.
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</section>

<!-- Statistics Section -->
<section class="py-5">
    <div class="container">
        <div class="row text-center">
            <div class="col-md-4">
                <div class="card border-0">
                    <div class="card-body">
                        <i class="bi bi-people-fill text-primary" style="font-size: 3rem;"></i>
                        <h3 class="mt-3"><?php echo e($calons->count()); ?></h3>
                        <p class="text-muted">Calon Terdaftar</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card border-0">
                    <div class="card-body">
                        <i class="bi bi-check-square-fill text-success" style="font-size: 3rem;"></i>
                        <h3 class="mt-3"><?php echo e(\App\Models\Suara::count()); ?></h3>
                        <p class="text-muted">Suara Masuk</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card border-0">
                    <div class="card-body">
                        <i class="bi bi-person-check-fill text-info" style="font-size: 3rem;"></i>
                        <h3 class="mt-3"><?php echo e(\App\Models\Pemilih::count()); ?></h3>
                        <p class="text-muted">Pemilih Terdaftar</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\pemilihan RT\rt\rt-voting-system\resources\views/home.blade.php ENDPATH**/ ?>