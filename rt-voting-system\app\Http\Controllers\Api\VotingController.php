<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Calon;
use App\Models\Suara;
use App\Models\Konfigurasi;

class VotingController extends Controller
{
    public function vote(Request $request)
    {
        $request->validate([
            'calon_id' => 'required|exists:calons,id',
        ]);

        $user = $request->user();
        $pemilih = $user->pemilih;

        if (!$pemilih) {
            return response()->json([
                'success' => false,
                'message' => 'Anda tidak terdaftar sebagai pemilih',
            ], 403);
        }

        if ($pemilih->hasVoted()) {
            return response()->json([
                'success' => false,
                'message' => 'Anda sudah memberikan suara',
            ], 403);
        }

        // Check if voting is active
        $config = Konfigurasi::getActive();
        if (!$config || !$config->isVotingActive()) {
            return response()->json([
                'success' => false,
                'message' => 'Pemilihan sedang tidak aktif',
            ], 403);
        }

        // Create vote
        Suara::create([
            'pemilih_id' => $pemilih->id,
            'calon_id' => $request->calon_id,
        ]);

        // Mark pemilih as voted
        $pemilih->markAsVoted();

        return response()->json([
            'success' => true,
            'message' => 'Suara berhasil disimpan',
        ]);
    }

    public function myVote(Request $request)
    {
        $user = $request->user();
        $pemilih = $user->pemilih;

        if (!$pemilih) {
            return response()->json([
                'success' => false,
                'message' => 'Anda tidak terdaftar sebagai pemilih',
            ], 403);
        }

        $suara = $pemilih->suara;
        if (!$suara) {
            return response()->json([
                'success' => false,
                'message' => 'Anda belum memberikan suara',
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => [
                'calon' => $suara->calon,
                'waktu' => $suara->waktu,
            ],
        ]);
    }

    public function votingStatus()
    {
        $config = Konfigurasi::getActive();

        return response()->json([
            'success' => true,
            'data' => [
                'is_active' => $config ? $config->isVotingActive() : false,
                'config' => $config,
            ],
        ]);
    }

    public function results()
    {
        $calons = Calon::withCount('suaras')->get();
        $totalVotes = Suara::count();

        return response()->json([
            'success' => true,
            'data' => [
                'calons' => $calons,
                'total_votes' => $totalVotes,
            ],
        ]);
    }
}
