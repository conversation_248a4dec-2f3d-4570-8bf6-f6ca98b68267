<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class AdminSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create admin user
        User::create([
            'name' => 'Admin RT',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role' => 'admin',
            'email_verified_at' => now(),
        ]);

        // Create sample pemilih users
        $pemilihs = [
            [
                'name' => '<PERSON><PERSON>',
                'email' => '<EMAIL>',
                'nik' => '3201234567890001',
                'alamat' => 'Jl. Merdeka No. 1, RT 01/RW 01',
                'tempat_lahir' => 'Jakarta',
                'tanggal_lahir' => '1985-05-15',
                'jenis_kelamin' => 'L',
                'pekerjaan' => 'Pegawai Swasta',
                'no_hp' => '081234567890'
            ],
            [
                'name' => 'Siti <PERSON>hayu',
                'email' => '<EMAIL>',
                'nik' => '3201234567890002',
                'alamat' => 'Jl. Merdeka No. 2, RT 01/RW 01',
                'tempat_lahir' => 'Bandung',
                'tanggal_lahir' => '1990-08-20',
                'jenis_kelamin' => 'P',
                'pekerjaan' => 'Ibu Rumah Tangga',
                'no_hp' => '081234567891'
            ],
            [
                'name' => 'Ahmad Wijaya',
                'email' => '<EMAIL>',
                'nik' => '3201234567890003',
                'alamat' => 'Jl. Merdeka No. 3, RT 01/RW 01',
                'tempat_lahir' => 'Surabaya',
                'tanggal_lahir' => '1988-12-10',
                'jenis_kelamin' => 'L',
                'pekerjaan' => 'Wiraswasta',
                'no_hp' => '081234567892'
            ],
        ];

        foreach ($pemilihs as $pemilihData) {
            $user = User::create([
                'name' => $pemilihData['name'],
                'email' => $pemilihData['email'],
                'password' => Hash::make('password123'),
                'role' => 'pemilih',
                'email_verified_at' => now(),
            ]);

            $user->pemilih()->create([
                'nik' => $pemilihData['nik'],
                'nama_lengkap' => $pemilihData['name'],
                'alamat' => $pemilihData['alamat'],
                'tempat_lahir' => $pemilihData['tempat_lahir'],
                'tanggal_lahir' => $pemilihData['tanggal_lahir'],
                'jenis_kelamin' => $pemilihData['jenis_kelamin'],
                'pekerjaan' => $pemilihData['pekerjaan'],
                'no_hp' => $pemilihData['no_hp'],
            ]);
        }
    }
}
