<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class AdminSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create admin user
        User::create([
            'name' => 'Admin RT',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role' => 'admin',
            'email_verified_at' => now(),
        ]);

        // Create sample pemilih users
        $pemilihs = [
            [
                'name' => '<PERSON><PERSON>o',
                'email' => '<EMAIL>',
                'nik' => '3201234567890001',
                'alamat' => 'Jl. Merdeka No. 1, RT 01/RW 01'
            ],
            [
                'name' => 'Siti Rahayu',
                'email' => '<EMAIL>',
                'nik' => '3201234567890002',
                'alamat' => 'Jl. Merdeka No. 2, RT 01/RW 01'
            ],
            [
                'name' => 'Ahmad Wijaya',
                'email' => '<EMAIL>',
                'nik' => '3201234567890003',
                'alamat' => 'Jl. Merdeka No. 3, RT 01/RW 01'
            ],
        ];

        foreach ($pemilihs as $pemilihData) {
            $user = User::create([
                'name' => $pemilihData['name'],
                'email' => $pemilihData['email'],
                'password' => Hash::make('password123'),
                'role' => 'pemilih',
                'email_verified_at' => now(),
            ]);

            $user->pemilih()->create([
                'nik' => $pemilihData['nik'],
                'nama_lengkap' => $pemilihData['name'],
                'alamat' => $pemilihData['alamat'],
            ]);
        }
    }
}
