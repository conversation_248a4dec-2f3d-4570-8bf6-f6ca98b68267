<?php $__env->startSection('title', 'Beranda - Sistem Pemilihan Ketua RT'); ?>

<?php $__env->startSection('content'); ?>
<!-- Hero Section with Enhanced Design -->
<section class="hero-section position-relative overflow-hidden">
    <div class="container">
        <div class="row align-items-center min-vh-75">
            <div class="col-lg-7">
                <div class="badge bg-primary bg-opacity-10 text-primary px-3 py-2 rounded-pill mb-3">
                    <i class="bi bi-shield-check me-1"></i> Sistem Voting Digital
                </div>
                <h1 class="display-3 fw-bold mb-4 text-gradient">
                    Pemilihan Ketua RT
                </h1>
                <p class="lead mb-4 text-muted">
                    <?php echo e($config->nama_event ?? 'Pemilihan Ketua RT Periode 2024-2027'); ?>

                </p>
                <p class="fs-5 mb-4 text-muted">
                    <?php echo e($config->deskripsi ?? 'Sistem pemilihan elektronik yang aman dan transparan untuk memilih ketua RT periode mendatang. Setiap warga yang terdaftar dapat memberikan suara secara online.'); ?>

                </p>

                <?php if($config && $config->isVotingActive()): ?>
                    <div class="alert alert-success border-0 shadow-sm d-inline-flex align-items-center">
                        <div class="spinner-grow spinner-grow-sm text-success me-3" role="status"></div>
                        <div>
                            <strong><i class="bi bi-broadcast"></i> Pemilihan Sedang Berlangsung!</strong><br>
                            <small>Berakhir: <?php echo e($config->waktu_selesai->format('d M Y, H:i')); ?></small>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="alert alert-warning border-0 shadow-sm d-inline-flex align-items-center">
                        <i class="bi bi-clock-history fs-4 me-3"></i>
                        <div>
                            <strong>Pemilihan Belum/Sudah Berakhir</strong><br>
                            <small>Silakan pantau pengumuman selanjutnya</small>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
            <div class="col-lg-5 text-center">
                <div class="position-relative">
                    <div class="bg-primary bg-opacity-10 rounded-circle p-5 d-inline-block">
                        <i class="bi bi-people-fill text-primary" style="font-size: 8rem;"></i>
                    </div>
                    <div class="position-absolute top-0 start-0 w-100 h-100">
                        <div class="floating-icons">
                            <i class="bi bi-check-circle-fill text-success position-absolute" style="top: 20%; left: 10%; font-size: 2rem; animation: float 3s ease-in-out infinite;"></i>
                            <i class="bi bi-shield-check text-primary position-absolute" style="top: 60%; right: 15%; font-size: 1.5rem; animation: float 3s ease-in-out infinite 1s;"></i>
                            <i class="bi bi-graph-up text-info position-absolute" style="bottom: 20%; left: 20%; font-size: 1.8rem; animation: float 3s ease-in-out infinite 2s;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Background decoration -->
    <div class="position-absolute top-0 end-0 translate-middle-y">
        <div class="bg-primary bg-opacity-5 rounded-circle" style="width: 300px; height: 300px;"></div>
    </div>
</section>

<!-- Enhanced Action Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <?php if(auth()->guard()->check()): ?>
                    <?php if(Auth::user()->isAdmin()): ?>
                        <div class="card border-0 shadow-lg bg-gradient-warning">
                            <div class="card-body text-center p-5">
                                <div class="mb-4">
                                    <div class="bg-white bg-opacity-20 rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 80px; height: 80px;">
                                        <i class="bi bi-shield-check text-white fs-1"></i>
                                    </div>
                                </div>
                                <h2 class="text-white mb-3">Selamat Datang, Administrator!</h2>
                                <p class="text-white mb-4 fs-5">Kelola sistem pemilihan melalui dashboard admin yang lengkap dan powerful.</p>
                                <div class="d-flex flex-wrap gap-3 justify-content-center">
                                    <a href="<?php echo e(route('admin.dashboard')); ?>" class="btn btn-light btn-lg px-4 py-3 shadow">
                                        <i class="bi bi-speedometer2 me-2"></i> Dashboard Admin
                                    </a>
                                    <a href="<?php echo e(route('result')); ?>" class="btn btn-outline-light btn-lg px-4 py-3">
                                        <i class="bi bi-bar-chart me-2"></i> Lihat Hasil
                                    </a>
                                </div>
                            </div>
                        </div>
                    <?php elseif(Auth::user()->pemilih && !Auth::user()->pemilih->hasVoted() && $config && $config->isVotingActive()): ?>
                        <div class="card border-0 shadow-lg bg-gradient-success">
                            <div class="card-body text-center p-5">
                                <div class="mb-4">
                                    <div class="bg-white bg-opacity-20 rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 80px; height: 80px;">
                                        <i class="bi bi-check-square text-white fs-1"></i>
                                    </div>
                                </div>
                                <h2 class="text-white mb-3">Siap untuk Memberikan Suara?</h2>
                                <p class="text-white mb-4 fs-5">Suara Anda sangat berharga untuk masa depan RT yang lebih baik.</p>
                                <div class="d-flex flex-wrap gap-3 justify-content-center">
                                    <a href="<?php echo e(route('voting')); ?>" class="btn btn-light btn-lg px-5 py-3 shadow pulse-button">
                                        <i class="bi bi-check-square me-2"></i> Mulai Voting
                                    </a>
                                    <a href="<?php echo e(route('result')); ?>" class="btn btn-outline-light btn-lg px-4 py-3">
                                        <i class="bi bi-bar-chart me-2"></i> Lihat Hasil
                                    </a>
                                </div>
                            </div>
                        </div>
                    <?php elseif(Auth::user()->pemilih && Auth::user()->pemilih->hasVoted()): ?>
                        <div class="card border-0 shadow-lg bg-gradient-info">
                            <div class="card-body text-center p-5">
                                <div class="mb-4">
                                    <div class="bg-white bg-opacity-20 rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 80px; height: 80px;">
                                        <i class="bi bi-check-circle text-white fs-1"></i>
                                    </div>
                                </div>
                                <h2 class="text-white mb-3">Terima Kasih!</h2>
                                <p class="text-white mb-4 fs-5">Anda sudah memberikan suara. Pantau hasil pemilihan secara real-time.</p>
                                <a href="<?php echo e(route('result')); ?>" class="btn btn-light btn-lg px-5 py-3 shadow">
                                    <i class="bi bi-bar-chart me-2"></i> Lihat Hasil Pemilihan
                                </a>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="card border-0 shadow-lg bg-gradient-secondary">
                            <div class="card-body text-center p-5">
                                <div class="mb-4">
                                    <div class="bg-white bg-opacity-20 rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 80px; height: 80px;">
                                        <i class="bi bi-exclamation-triangle text-white fs-1"></i>
                                    </div>
                                </div>
                                <h2 class="text-white mb-3">Pemilihan Tidak Aktif</h2>
                                <p class="text-white mb-4 fs-5">Pemilihan sedang tidak aktif atau Anda tidak terdaftar sebagai pemilih.</p>
                                <a href="<?php echo e(route('result')); ?>" class="btn btn-light btn-lg px-5 py-3 shadow">
                                    <i class="bi bi-bar-chart me-2"></i> Lihat Hasil
                                </a>
                            </div>
                        </div>
                    <?php endif; ?>
                <?php else: ?>
                    <div class="card border-0 shadow-lg bg-gradient-primary">
                        <div class="card-body text-center p-5">
                            <div class="mb-4">
                                <div class="bg-white bg-opacity-20 rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 80px; height: 80px;">
                                    <i class="bi bi-box-arrow-in-right text-white fs-1"></i>
                                </div>
                            </div>
                            <h2 class="text-white mb-3">Masuk untuk Memberikan Suara</h2>
                            <p class="text-white mb-4 fs-5">Login dengan akun yang telah terdaftar untuk berpartisipasi dalam pemilihan.</p>
                            <div class="d-flex flex-wrap gap-3 justify-content-center">
                                <a href="<?php echo e(route('login')); ?>" class="btn btn-light btn-lg px-5 py-3 shadow">
                                    <i class="bi bi-box-arrow-in-right me-2"></i> Login Sekarang
                                </a>
                                <a href="<?php echo e(route('result')); ?>" class="btn btn-outline-light btn-lg px-4 py-3">
                                    <i class="bi bi-bar-chart me-2"></i> Lihat Hasil
                                </a>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>

<!-- Enhanced Statistics Section -->
<section class="py-5">
    <div class="container">
        <div class="text-center mb-5">
            <div class="badge bg-primary bg-opacity-10 text-primary px-3 py-2 rounded-pill mb-3">
                <i class="bi bi-graph-up me-1"></i> Real-time Data
            </div>
            <h2 class="display-5 fw-bold mb-3">Statistik Pemilihan</h2>
            <p class="text-muted fs-5">Data terkini dari sistem pemilihan elektronik</p>
        </div>

        <div class="row g-4">
            <div class="col-lg-3 col-md-6">
                <div class="card border-0 shadow-sm h-100 hover-lift">
                    <div class="card-body text-center p-4">
                        <div class="bg-primary bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 70px; height: 70px;">
                            <i class="bi bi-people-fill text-primary fs-2"></i>
                        </div>
                        <h3 class="fw-bold text-primary mb-2"><?php echo e($calons->count()); ?></h3>
                        <p class="text-muted mb-0">Calon Terdaftar</p>
                        <small class="text-success">
                            <i class="bi bi-check-circle"></i> Terverifikasi
                        </small>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="card border-0 shadow-sm h-100 hover-lift">
                    <div class="card-body text-center p-4">
                        <div class="bg-success bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 70px; height: 70px;">
                            <i class="bi bi-check-square-fill text-success fs-2"></i>
                        </div>
                        <h3 class="fw-bold text-success mb-2 counter" data-target="<?php echo e($totalVotes); ?>">0</h3>
                        <p class="text-muted mb-0">Suara Masuk</p>
                        <small class="text-success">
                            <i class="bi bi-arrow-up"></i> Live Count
                        </small>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="card border-0 shadow-sm h-100 hover-lift">
                    <div class="card-body text-center p-4">
                        <div class="bg-info bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 70px; height: 70px;">
                            <i class="bi bi-person-check-fill text-info fs-2"></i>
                        </div>
                        <h3 class="fw-bold text-info mb-2"><?php echo e($totalPemilih); ?></h3>
                        <p class="text-muted mb-0">Pemilih Terdaftar</p>
                        <small class="text-info">
                            <i class="bi bi-shield-check"></i> DPT Valid
                        </small>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="card border-0 shadow-sm h-100 hover-lift">
                    <div class="card-body text-center p-4">
                        <div class="bg-warning bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 70px; height: 70px;">
                            <i class="bi bi-percent text-warning fs-2"></i>
                        </div>
                        <?php
                            $participation = $totalPemilih > 0 ? ($totalVotes / $totalPemilih) * 100 : 0;
                        ?>
                        <h3 class="fw-bold text-warning mb-2"><?php echo e(number_format($participation, 1)); ?>%</h3>
                        <p class="text-muted mb-0">Partisipasi</p>
                        <div class="progress mt-2" style="height: 6px;">
                            <div class="progress-bar bg-warning" role="progressbar" style="width: <?php echo e($participation); ?>%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Additional Stats Row -->
        <div class="row mt-5">
            <div class="col-12">
                <div class="card border-0 shadow-sm bg-gradient-primary text-white">
                    <div class="card-body p-4">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <h5 class="mb-2">
                                    <i class="bi bi-clock-history me-2"></i>
                                    Status Pemilihan
                                </h5>
                                <?php if($config && $config->isVotingActive()): ?>
                                    <p class="mb-0 fs-6">
                                        <span class="badge bg-success me-2">AKTIF</span>
                                        Pemilihan berlangsung hingga <?php echo e($config->waktu_selesai->format('d M Y, H:i')); ?>

                                    </p>
                                <?php else: ?>
                                    <p class="mb-0 fs-6">
                                        <span class="badge bg-warning me-2">TIDAK AKTIF</span>
                                        Pemilihan sedang tidak berlangsung
                                    </p>
                                <?php endif; ?>
                            </div>
                            <div class="col-md-4 text-md-end">
                                <div class="d-flex align-items-center justify-content-md-end">
                                    <div class="me-3">
                                        <small class="d-block">Sistem</small>
                                        <span class="badge bg-success">Online</span>
                                    </div>
                                    <div class="bg-white bg-opacity-20 rounded-circle p-2">
                                        <i class="bi bi-shield-check fs-4"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Enhanced Candidates Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="text-center mb-5">
            <div class="badge bg-success bg-opacity-10 text-success px-3 py-2 rounded-pill mb-3">
                <i class="bi bi-people me-1"></i> Kandidat Terpilih
            </div>
            <h2 class="display-5 fw-bold mb-3">Calon Ketua RT</h2>
            <p class="text-muted fs-5">Kenali profil dan visi-misi para calon ketua RT</p>
        </div>

        <div class="row g-4">
            <?php $__empty_1 = true; $__currentLoopData = $calons; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $calon): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <div class="col-lg-4 col-md-6">
                    <div class="card card-calon h-100 border-0 shadow-sm">
                        <div class="card-header bg-white border-0 text-center pt-4">
                            <div class="position-relative d-inline-block">
                                <div class="bg-gradient-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center shadow-lg"
                                     style="width: 100px; height: 100px; font-size: 2.5rem; font-weight: bold;">
                                    <?php echo e($calon->nomor_urut); ?>

                                </div>
                                <div class="position-absolute bottom-0 end-0">
                                    <span class="badge bg-success rounded-circle p-2">
                                        <i class="bi bi-check"></i>
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="card-body text-center px-4">
                            <h4 class="card-title fw-bold mb-3"><?php echo e($calon->nama); ?></h4>

                            <div class="mb-4">
                                <div class="bg-primary bg-opacity-10 rounded-3 p-3 mb-3">
                                    <h6 class="text-primary mb-2">
                                        <i class="bi bi-eye me-1"></i> Visi
                                    </h6>
                                    <p class="small text-muted mb-0"><?php echo e(Str::limit($calon->visi, 120)); ?></p>
                                </div>

                                <div class="bg-success bg-opacity-10 rounded-3 p-3 mb-3">
                                    <h6 class="text-success mb-2">
                                        <i class="bi bi-target me-1"></i> Misi
                                    </h6>
                                    <p class="small text-muted mb-0"><?php echo e(Str::limit($calon->misi, 120)); ?></p>
                                </div>

                                <?php if($calon->rekam_jejak): ?>
                                    <div class="bg-info bg-opacity-10 rounded-3 p-3">
                                        <h6 class="text-info mb-2">
                                            <i class="bi bi-award me-1"></i> Rekam Jejak
                                        </h6>
                                        <p class="small text-muted mb-0"><?php echo e(Str::limit($calon->rekam_jejak, 100)); ?></p>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="card-footer bg-white border-0 text-center pb-4">
                            <?php if(auth()->guard()->check()): ?>
                                <?php if(Auth::user()->isPemilih() && !Auth::user()->pemilih->hasVoted() && $config && $config->isVotingActive()): ?>
                                    <a href="<?php echo e(route('voting')); ?>" class="btn btn-gradient-primary btn-sm px-4">
                                        <i class="bi bi-check-square me-1"></i> Pilih Calon Ini
                                    </a>
                                <?php endif; ?>
                            <?php endif; ?>
                            <div class="mt-2">
                                <small class="text-muted">
                                    <i class="bi bi-person-badge"></i> Calon #<?php echo e($calon->nomor_urut); ?>

                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <div class="col-12">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body text-center py-5">
                            <div class="mb-4">
                                <i class="bi bi-people text-muted" style="font-size: 4rem;"></i>
                            </div>
                            <h5 class="text-muted">Belum Ada Calon Terdaftar</h5>
                            <p class="text-muted">Calon ketua RT akan ditampilkan di sini setelah pendaftaran dibuka.</p>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</section>

<!-- Enhanced Information Section -->
<section class="py-5">
    <div class="container">
        <div class="text-center mb-5">
            <div class="badge bg-info bg-opacity-10 text-info px-3 py-2 rounded-pill mb-3">
                <i class="bi bi-info-circle me-1"></i> Panduan & Keamanan
            </div>
            <h2 class="display-5 fw-bold mb-3">Informasi Penting</h2>
            <p class="text-muted fs-5">Ketahui cara voting dan jaminan keamanan sistem</p>
        </div>

        <div class="row g-4 mb-5">
            <div class="col-lg-6">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body p-4">
                        <div class="d-flex align-items-center mb-4">
                            <div class="bg-primary bg-opacity-10 rounded-circle p-3 me-3">
                                <i class="bi bi-list-check text-primary fs-4"></i>
                            </div>
                            <h4 class="mb-0 fw-bold">Cara Voting</h4>
                        </div>
                        <div class="steps">
                            <div class="d-flex mb-3">
                                <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 30px; height: 30px; font-size: 0.8rem;">1</div>
                                <div>
                                    <strong>Login</strong><br>
                                    <small class="text-muted">Masuk dengan akun yang telah terdaftar</small>
                                </div>
                            </div>
                            <div class="d-flex mb-3">
                                <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 30px; height: 30px; font-size: 0.8rem;">2</div>
                                <div>
                                    <strong>Pilih Calon</strong><br>
                                    <small class="text-muted">Pilih salah satu calon yang tersedia</small>
                                </div>
                            </div>
                            <div class="d-flex mb-3">
                                <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 30px; height: 30px; font-size: 0.8rem;">3</div>
                                <div>
                                    <strong>Konfirmasi</strong><br>
                                    <small class="text-muted">Pastikan pilihan Anda sudah benar</small>
                                </div>
                            </div>
                            <div class="d-flex">
                                <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 30px; height: 30px; font-size: 0.8rem;">4</div>
                                <div>
                                    <strong>Selesai</strong><br>
                                    <small class="text-muted">Suara tersimpan dan tidak dapat diubah</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-6">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body p-4">
                        <div class="d-flex align-items-center mb-4">
                            <div class="bg-success bg-opacity-10 rounded-circle p-3 me-3">
                                <i class="bi bi-shield-check text-success fs-4"></i>
                            </div>
                            <h4 class="mb-0 fw-bold">Jaminan Keamanan</h4>
                        </div>
                        <div class="security-features">
                            <div class="d-flex align-items-center mb-3">
                                <i class="bi bi-check-circle-fill text-success me-3"></i>
                                <div>
                                    <strong>One Person, One Vote</strong><br>
                                    <small class="text-muted">Setiap pemilih hanya dapat memilih sekali</small>
                                </div>
                            </div>
                            <div class="d-flex align-items-center mb-3">
                                <i class="bi bi-lock-fill text-success me-3"></i>
                                <div>
                                    <strong>Data Terenkripsi</strong><br>
                                    <small class="text-muted">Semua data voting dilindungi enkripsi</small>
                                </div>
                            </div>
                            <div class="d-flex align-items-center mb-3">
                                <i class="bi bi-eye-fill text-success me-3"></i>
                                <div>
                                    <strong>Transparan</strong><br>
                                    <small class="text-muted">Hasil dapat dipantau secara real-time</small>
                                </div>
                            </div>
                            <div class="d-flex align-items-center">
                                <i class="bi bi-clipboard-check text-success me-3"></i>
                                <div>
                                    <strong>Dapat Diaudit</strong><br>
                                    <small class="text-muted">Sistem dapat diverifikasi dan diaudit</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- FAQ Section -->
        <div class="row">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-body p-4">
                        <h4 class="fw-bold mb-4 text-center">
                            <i class="bi bi-question-circle text-info me-2"></i>
                            Pertanyaan Umum (FAQ)
                        </h4>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-4">
                                    <h6 class="fw-bold text-primary">Kapan waktu pemilihan?</h6>
                                    <p class="text-muted mb-0">
                                        <?php if($config): ?>
                                            Pemilihan berlangsung dari <?php echo e($config->waktu_mulai->format('d M Y, H:i')); ?>

                                            hingga <?php echo e($config->waktu_selesai->format('d M Y, H:i')); ?>.
                                        <?php else: ?>
                                            Waktu pemilihan akan diumumkan kemudian.
                                        <?php endif; ?>
                                    </p>
                                </div>
                                <div class="mb-4">
                                    <h6 class="fw-bold text-primary">Siapa yang bisa voting?</h6>
                                    <p class="text-muted mb-0">Hanya warga yang terdaftar dalam Daftar Pemilih Tetap (DPT) yang dapat memberikan suara.</p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-4">
                                    <h6 class="fw-bold text-primary">Apakah suara saya rahasia?</h6>
                                    <p class="text-muted mb-0">Ya, sistem menjamin kerahasiaan suara Anda dengan enkripsi tingkat tinggi.</p>
                                </div>
                                <div class="mb-4">
                                    <h6 class="fw-bold text-primary">Bagaimana jika lupa password?</h6>
                                    <p class="text-muted mb-0">Hubungi panitia pemilihan untuk reset password atau bantuan teknis lainnya.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Call to Action Section -->
<section class="py-5 bg-gradient-primary text-white">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h3 class="fw-bold mb-3">Siap Berpartisipasi dalam Pemilihan?</h3>
                <p class="fs-5 mb-0">Suara Anda menentukan masa depan RT. Mari bersama-sama membangun lingkungan yang lebih baik.</p>
            </div>
            <div class="col-lg-4 text-lg-end">
                <?php if(auth()->guard()->check()): ?>
                    <?php if(Auth::user()->isPemilih() && !Auth::user()->pemilih->hasVoted() && $config && $config->isVotingActive()): ?>
                        <a href="<?php echo e(route('voting')); ?>" class="btn btn-light btn-lg px-5 py-3 shadow">
                            <i class="bi bi-check-square me-2"></i> Mulai Voting
                        </a>
                    <?php else: ?>
                        <a href="<?php echo e(route('result')); ?>" class="btn btn-light btn-lg px-5 py-3 shadow">
                            <i class="bi bi-bar-chart me-2"></i> Lihat Hasil
                        </a>
                    <?php endif; ?>
                <?php else: ?>
                    <a href="<?php echo e(route('login')); ?>" class="btn btn-light btn-lg px-5 py-3 shadow">
                        <i class="bi bi-box-arrow-in-right me-2"></i> Login Sekarang
                    </a>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\pemilihan RT\rt\rt-voting-system\resources\views/homepage.blade.php ENDPATH**/ ?>