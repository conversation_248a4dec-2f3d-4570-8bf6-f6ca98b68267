@extends('admin.layout')

@section('title', 'Dashboard Admin')
@section('page-title', 'Dashboard')

@section('content')
<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card stats-card">
            <div class="card-body text-center">
                <i class="bi bi-people-fill" style="font-size: 2.5rem;"></i>
                <h3 class="mt-2">{{ $stats['total_pemilih'] }}</h3>
                <p class="mb-0">Total Pemilih</p>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <i class="bi bi-check-square-fill" style="font-size: 2.5rem;"></i>
                <h3 class="mt-2">{{ $stats['total_suara'] }}</h3>
                <p class="mb-0"><PERSON><PERSON></p>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <i class="bi bi-person-check-fill" style="font-size: 2.5rem;"></i>
                <h3 class="mt-2">{{ $stats['pemilih_sudah_voting'] }}</h3>
                <p class="mb-0">Sudah Voting</p>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-warning text-white">
            <div class="card-body text-center">
                <i class="bi bi-person-x-fill" style="font-size: 2.5rem;"></i>
                <h3 class="mt-2">{{ $stats['total_pemilih'] - $stats['pemilih_sudah_voting'] }}</h3>
                <p class="mb-0">Belum Voting</p>
            </div>
        </div>
    </div>
</div>

<!-- Participation Rate -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-graph-up"></i> Tingkat Partisipasi
                </h5>
            </div>
            <div class="card-body">
                @php
                    $participation = $stats['total_pemilih'] > 0 ? ($stats['pemilih_sudah_voting'] / $stats['total_pemilih']) * 100 : 0;
                @endphp
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <div class="progress" style="height: 30px;">
                            <div class="progress-bar bg-success"
                                 role="progressbar"
                                 style="width: {{ $participation }}%"
                                 aria-valuenow="{{ $participation }}"
                                 aria-valuemin="0"
                                 aria-valuemax="100">
                                <strong>{{ number_format($participation, 1) }}%</strong>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 text-center">
                        <h4 class="text-success mb-0">{{ number_format($participation, 1) }}%</h4>
                        <small class="text-muted">Partisipasi Pemilih</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-lightning"></i> Aksi Cepat
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-2">
                        <a href="{{ route('admin.pemilih.create') }}" class="btn btn-primary w-100">
                            <i class="bi bi-person-plus"></i> Tambah Pemilih
                        </a>
                    </div>
                    <div class="col-md-4 mb-2">
                        <a href="{{ route('admin.pemilih.index') }}" class="btn btn-info w-100">
                            <i class="bi bi-people"></i> Kelola Pemilih
                        </a>
                    </div>
                    <div class="col-md-4 mb-2">
                        <a href="{{ route('result') }}" class="btn btn-success w-100">
                            <i class="bi bi-bar-chart"></i> Lihat Hasil
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Votes -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="bi bi-clock-history"></i> Suara Terbaru
                </h5>
                <small class="text-muted">10 suara terakhir</small>
            </div>
            <div class="card-body">
                @if($recentVotes->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Waktu</th>
                                    <th>Pemilih</th>
                                    <th>NIK</th>
                                    <th>Pilihan</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($recentVotes as $vote)
                                    <tr>
                                        <td>
                                            <small>{{ $vote->waktu->format('d/m/Y H:i') }}</small>
                                        </td>
                                        <td>{{ $vote->pemilih->nama_lengkap }}</td>
                                        <td>
                                            <small class="text-muted">{{ $vote->pemilih->nik }}</small>
                                        </td>
                                        <td>
                                            <span class="badge bg-primary">
                                                {{ $vote->calon->nomor_urut }}. {{ $vote->calon->nama }}
                                            </span>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <div class="text-center text-muted py-4">
                        <i class="bi bi-inbox" style="font-size: 3rem;"></i>
                        <p class="mt-2">Belum ada suara yang masuk</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
// Auto refresh every 30 seconds
setTimeout(function() {
    location.reload();
}, 30000);

// Add animation to progress bar
document.addEventListener('DOMContentLoaded', function() {
    const progressBar = document.querySelector('.progress-bar');
    if (progressBar) {
        const width = progressBar.style.width;
        progressBar.style.width = '0%';
        setTimeout(() => {
            progressBar.style.transition = 'width 1s ease-in-out';
            progressBar.style.width = width;
        }, 100);
    }
});
</script>
@endsection
