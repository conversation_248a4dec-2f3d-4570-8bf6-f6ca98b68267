<?php $__env->startSection('title', 'Login - Sistem Pemilihan Ketua RT'); ?>

<?php $__env->startSection('content'); ?>
<div class="min-vh-100 d-flex align-items-center bg-light">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-5 col-md-7">
                <div class="card border-0 shadow-lg">
                    <!-- Header with gradient -->
                    <div class="card-header bg-gradient-primary text-white text-center border-0 py-4">
                        <div class="mb-3">
                            <div class="bg-white bg-opacity-20 rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 80px; height: 80px;">
                                <i class="bi bi-box-arrow-in-right text-white" style="font-size: 2.5rem;"></i>
                            </div>
                        </div>
                        <h3 class="mb-1 fw-bold">Selamat Datang</h3>
                        <p class="mb-0 opacity-75">Masuk ke Sistem Pemilihan Ketua RT</p>
                    </div>

                    <div class="card-body p-5">
                        <?php if($errors->any()): ?>
                            <div class="alert alert-danger border-0 shadow-sm" role="alert">
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                    <div>
                                        <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <div><?php echo e($error); ?></div>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>

                        <form method="POST" action="<?php echo e(route('login.post')); ?>" id="loginForm">
                            <?php echo csrf_field(); ?>

                            <div class="mb-4">
                                <label for="email" class="form-label fw-bold">
                                    <i class="bi bi-envelope me-1 text-primary"></i>Email
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text bg-light border-end-0">
                                        <i class="bi bi-person text-muted"></i>
                                    </span>
                                    <input type="email"
                                           class="form-control border-start-0 ps-0 <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                           id="email"
                                           name="email"
                                           placeholder="Masukkan email Anda"
                                           value="<?php echo e(old('email')); ?>"
                                           required
                                           autofocus>
                                </div>
                                <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="text-danger small mt-1"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <div class="mb-4">
                                <label for="password" class="form-label fw-bold">
                                    <i class="bi bi-lock me-1 text-primary"></i>Password
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text bg-light border-end-0">
                                        <i class="bi bi-key text-muted"></i>
                                    </span>
                                    <input type="password"
                                           class="form-control border-start-0 border-end-0 ps-0 <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                           id="password"
                                           name="password"
                                           placeholder="Masukkan password Anda"
                                           required>
                                    <button class="btn btn-outline-secondary border-start-0"
                                            type="button"
                                            onclick="togglePassword()"
                                            tabindex="-1">
                                        <i class="bi bi-eye" id="toggleIcon"></i>
                                    </button>
                                </div>
                                <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="text-danger small mt-1"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <div class="mb-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="remember" name="remember">
                                    <label class="form-check-label text-muted" for="remember">
                                        Ingat saya
                                    </label>
                                </div>
                            </div>

                            <div class="d-grid mb-4">
                                <button type="submit" class="btn btn-primary btn-lg py-3 fw-bold" id="loginBtn">
                                    <i class="bi bi-box-arrow-in-right me-2"></i>
                                    <span id="loginText">Masuk Sekarang</span>
                                </button>
                            </div>
                        </form>

                        <!-- Demo Accounts Section -->
                        <div class="border-top pt-4">
                            <h6 class="text-center text-muted mb-3">
                                <i class="bi bi-info-circle me-1"></i>Akun Demo untuk Testing
                            </h6>
                            <div class="row g-3">
                                <div class="col-12">
                                    <div class="card bg-warning bg-opacity-10 border-warning border-2">
                                        <div class="card-body p-3">
                                            <div class="d-flex align-items-center">
                                                <div class="bg-warning bg-opacity-20 rounded-circle p-2 me-3">
                                                    <i class="bi bi-shield-check text-warning"></i>
                                                </div>
                                                <div class="flex-grow-1">
                                                    <h6 class="mb-1 fw-bold">Administrator</h6>
                                                    <div class="small">
                                                        <code class="bg-dark text-warning px-2 py-1 rounded"><EMAIL></code>
                                                        <span class="mx-1">/</span>
                                                        <code class="bg-dark text-warning px-2 py-1 rounded">password123</code>
                                                    </div>
                                                    <small class="text-muted">→ Dashboard Admin</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <div class="card bg-primary bg-opacity-10 border-primary border-2">
                                        <div class="card-body p-3">
                                            <div class="d-flex align-items-center">
                                                <div class="bg-primary bg-opacity-20 rounded-circle p-2 me-3">
                                                    <i class="bi bi-person-check text-primary"></i>
                                                </div>
                                                <div class="flex-grow-1">
                                                    <h6 class="mb-1 fw-bold">Pemilih</h6>
                                                    <div class="small">
                                                        <code class="bg-dark text-info px-2 py-1 rounded"><EMAIL></code>
                                                        <span class="mx-1">/</span>
                                                        <code class="bg-dark text-info px-2 py-1 rounded">password123</code>
                                                    </div>
                                                    <small class="text-muted">→ Halaman Voting</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Footer -->
                    <div class="card-footer bg-light border-0 text-center py-3">
                        <small class="text-muted">
                            <i class="bi bi-shield-lock me-1"></i>
                            Login aman dengan enkripsi SSL
                        </small>
                    </div>
                </div>

                <!-- Back to Home -->
                <div class="text-center mt-4">
                    <a href="<?php echo e(route('home')); ?>" class="btn btn-outline-primary">
                        <i class="bi bi-arrow-left me-1"></i>Kembali ke Beranda
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function togglePassword() {
    const passwordInput = document.getElementById('password');
    const toggleIcon = document.getElementById('toggleIcon');

    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        toggleIcon.className = 'bi bi-eye-slash';
    } else {
        passwordInput.type = 'password';
        toggleIcon.className = 'bi bi-eye';
    }
}

// Form submission with loading state
document.getElementById('loginForm').addEventListener('submit', function() {
    const loginBtn = document.getElementById('loginBtn');
    const loginText = document.getElementById('loginText');

    loginBtn.disabled = true;
    loginText.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Memproses...';
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\pemilihan RT\rt\rt-voting-system\resources\views/login.blade.php ENDPATH**/ ?>