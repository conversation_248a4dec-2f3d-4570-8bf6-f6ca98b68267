<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Calon;
use App\Models\User;

class CalonSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $admin = User::where('role', 'admin')->first();

        $calons = [
            [
                'nama' => 'Pak Joko <PERSON>',
                'visi' => 'Membangun RT yang maju, sejahtera, dan harmonis untuk semua warga.',
                'misi' => 'Meningkatkan fasilitas umum, keamanan lingkungan, dan gotong royong antar warga.',
                'rekam_jejak' => 'Mantan Ketua RW selama 5 tahun, aktif dalam kegiatan sosial masyarakat.',
                'nomor_urut' => 1,
            ],
            [
                'nama' => 'Ibu Mega Sari',
                'visi' => 'RT yang bersih, hijau, dan peduli lingkungan untuk generasi mendatang.',
                'misi' => 'Mengembangkan program keb<PERSON><PERSON>an, pengh<PERSON><PERSON>, dan pemberdayaan ibu-ibu PKK.',
                'rekam_jejak' => 'Ketua PKK RT selama 3 tahun, koordinator program bank sampah.',
                'nomor_urut' => 2,
            ],
            [
                'nama' => 'Bapak Susilo',
                'visi' => 'Menciptakan RT yang aman, nyaman, dan sejahtera bagi seluruh warga.',
                'misi' => 'Meningkatkan sistem keamanan, infrastruktur jalan, dan program ekonomi kreatif.',
                'rekam_jejak' => 'Koordinator Siskamling selama 4 tahun, pengusaha lokal yang sukses.',
                'nomor_urut' => 3,
            ],
        ];

        foreach ($calons as $calonData) {
            Calon::create([
                'nama' => $calonData['nama'],
                'visi' => $calonData['visi'],
                'misi' => $calonData['misi'],
                'rekam_jejak' => $calonData['rekam_jejak'],
                'nomor_urut' => $calonData['nomor_urut'],
                'created_by' => $admin->id,
                'is_active' => true,
            ]);
        }
    }
}
