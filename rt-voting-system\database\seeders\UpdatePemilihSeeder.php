<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Pemilih;

class UpdatePemilihSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $pemilihs = Pemilih::all();
        $data = [
            [
                'tempat_lahir' => 'Jakarta',
                'tanggal_lahir' => '1985-05-15',
                'jenis_kelamin' => 'L',
                'pekerjaan' => 'Pegawai Swasta',
                'no_hp' => '081234567890'
            ],
            [
                'tempat_lahir' => 'Bandung',
                'tanggal_lahir' => '1990-08-20',
                'jenis_kelamin' => 'P',
                'pekerjaan' => 'Ibu Rumah <PERSON>ga',
                'no_hp' => '081234567891'
            ],
            [
                'tempat_lahir' => 'Surabaya',
                'tanggal_lahir' => '1988-12-10',
                'jenis_kelamin' => 'L',
                'pekerjaan' => 'Wiraswasta',
                'no_hp' => '081234567892'
            ]
        ];

        foreach($pemilihs as $index => $pemilih) {
            if(isset($data[$index])) {
                $pemilih->update($data[$index]);
            }
        }
    }
}
