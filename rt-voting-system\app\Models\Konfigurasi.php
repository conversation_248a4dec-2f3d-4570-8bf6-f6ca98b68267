<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Konfigurasi extends Model
{
    use HasFactory;

    protected $fillable = [
        'nama_event',
        'deskripsi',
        'waktu_mulai',
        'waktu_selesai',
        'is_active',
        'pengaturan_tambahan',
    ];

    protected $casts = [
        'waktu_mulai' => 'datetime',
        'waktu_selesai' => 'datetime',
        'is_active' => 'boolean',
        'pengaturan_tambahan' => 'array',
    ];

    /**
     * Check if voting is currently active
     */
    public function isVotingActive(): bool
    {
        $now = now();
        return $this->is_active &&
               $now->greaterThanOrEqualTo($this->waktu_mulai) &&
               $now->lessThanOrEqualTo($this->waktu_selesai);
    }

    /**
     * Get the active configuration
     */
    public static function getActive()
    {
        return static::where('is_active', true)->first();
    }

    /**
     * Scope for active configurations
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }
}
