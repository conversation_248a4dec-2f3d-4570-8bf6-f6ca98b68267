<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Pemilih;
use App\Models\Calon;
use App\Models\Suara;
use App\Models\Konfigurasi;
use Illuminate\Support\Facades\Hash;

class AdminController extends Controller
{
    public function dashboard()
    {
        $stats = [
            'total_pemilih' => Pemilih::count(),
            'total_calon' => Calon::count(),
            'total_suara' => Suara::count(),
            'pemilih_sudah_voting' => Pemilih::where('status_memilih', 'sudah')->count(),
        ];

        $recentVotes = Suara::with(['pemilih', 'calon'])
            ->latest()
            ->take(10)
            ->get();

        return view('admin.dashboard', compact('stats', 'recentVotes'));
    }

    // CRUD Pemilih
    public function pemilihIndex()
    {
        $pemilihs = Pemilih::with('user')->paginate(15);
        return view('admin.pemilih.index', compact('pemilihs'));
    }

    public function pemilihCreate()
    {
        return view('admin.pemilih.create');
    }

    public function pemilihStore(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email',
            'password' => 'required|min:6',
            'nik' => 'required|string|size:16|unique:pemilihs,nik',
            'nama_lengkap' => 'required|string|max:255',
            'alamat' => 'required|string',
            'tempat_lahir' => 'required|string|max:255',
            'tanggal_lahir' => 'required|date',
            'jenis_kelamin' => 'required|in:L,P',
            'pekerjaan' => 'required|string|max:255',
            'no_hp' => 'required|string|max:15',
        ]);

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'role' => 'pemilih',
            'email_verified_at' => now(),
        ]);

        $user->pemilih()->create([
            'nik' => $request->nik,
            'nama_lengkap' => $request->nama_lengkap,
            'alamat' => $request->alamat,
            'tempat_lahir' => $request->tempat_lahir,
            'tanggal_lahir' => $request->tanggal_lahir,
            'jenis_kelamin' => $request->jenis_kelamin,
            'pekerjaan' => $request->pekerjaan,
            'no_hp' => $request->no_hp,
        ]);

        return redirect()->route('admin.pemilih.index')
            ->with('success', 'Pemilih berhasil ditambahkan');
    }

    public function pemilihEdit(Pemilih $pemilih)
    {
        return view('admin.pemilih.edit', compact('pemilih'));
    }

    public function pemilihUpdate(Request $request, Pemilih $pemilih)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email,' . $pemilih->user_id,
            'nik' => 'required|string|size:16|unique:pemilihs,nik,' . $pemilih->id,
            'nama_lengkap' => 'required|string|max:255',
            'alamat' => 'required|string',
            'tempat_lahir' => 'required|string|max:255',
            'tanggal_lahir' => 'required|date',
            'jenis_kelamin' => 'required|in:L,P',
            'pekerjaan' => 'required|string|max:255',
            'no_hp' => 'required|string|max:15',
        ]);

        $pemilih->user->update([
            'name' => $request->name,
            'email' => $request->email,
        ]);

        if ($request->password) {
            $pemilih->user->update([
                'password' => Hash::make($request->password),
            ]);
        }

        $pemilih->update([
            'nik' => $request->nik,
            'nama_lengkap' => $request->nama_lengkap,
            'alamat' => $request->alamat,
            'tempat_lahir' => $request->tempat_lahir,
            'tanggal_lahir' => $request->tanggal_lahir,
            'jenis_kelamin' => $request->jenis_kelamin,
            'pekerjaan' => $request->pekerjaan,
            'no_hp' => $request->no_hp,
        ]);

        return redirect()->route('admin.pemilih.index')
            ->with('success', 'Pemilih berhasil diupdate');
    }

    public function pemilihDestroy(Pemilih $pemilih)
    {
        $pemilih->user->delete(); // Will cascade delete pemilih
        return redirect()->route('admin.pemilih.index')
            ->with('success', 'Pemilih berhasil dihapus');
    }

    // CRUD Admin
    public function adminIndex()
    {
        $admins = User::where('role', 'admin')->paginate(15);
        return view('admin.admin.index', compact('admins'));
    }

    public function adminCreate()
    {
        return view('admin.admin.create');
    }

    public function adminStore(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email',
            'password' => 'required|min:6',
        ]);

        User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'role' => 'admin',
            'email_verified_at' => now(),
        ]);

        return redirect()->route('admin.admin.index')
            ->with('success', 'Admin berhasil ditambahkan');
    }

    public function adminEdit(User $admin)
    {
        if ($admin->role !== 'admin') {
            return redirect()->route('admin.admin.index')
                ->with('error', 'User bukan admin');
        }

        return view('admin.admin.edit', compact('admin'));
    }

    public function adminUpdate(Request $request, User $admin)
    {
        if ($admin->role !== 'admin') {
            return redirect()->route('admin.admin.index')
                ->with('error', 'User bukan admin');
        }

        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email,' . $admin->id,
        ]);

        $admin->update([
            'name' => $request->name,
            'email' => $request->email,
        ]);

        if ($request->password) {
            $admin->update([
                'password' => Hash::make($request->password),
            ]);
        }

        return redirect()->route('admin.admin.index')
            ->with('success', 'Admin berhasil diupdate');
    }

    public function adminDestroy(User $admin)
    {
        if ($admin->role !== 'admin') {
            return redirect()->route('admin.admin.index')
                ->with('error', 'User bukan admin');
        }

        if ($admin->id === auth()->id()) {
            return redirect()->route('admin.admin.index')
                ->with('error', 'Tidak dapat menghapus akun sendiri');
        }

        $admin->delete();
        return redirect()->route('admin.admin.index')
            ->with('success', 'Admin berhasil dihapus');
    }
}
