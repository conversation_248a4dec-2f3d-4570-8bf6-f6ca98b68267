@extends('layout')

@section('title', 'Has<PERSON> - Sistem Pemilihan Ketua RT')

@section('content')
<section class="py-5">
    <div class="container">
        <!-- Header -->
        <div class="row justify-content-center mb-5">
            <div class="col-md-8 text-center">
                <h2 class="mb-3">
                    <i class="bi bi-bar-chart"></i> Hasil Pemilihan Ketua RT
                </h2>
                <p class="lead text-muted">
                    Hasil sementara pemilihan ketua RT
                </p>
                <div class="alert alert-info">
                    <i class="bi bi-people"></i>
                    <strong>Total Suara Masuk:</strong> {{ $totalVotes }} suara
                </div>
            </div>
        </div>

        <!-- Results -->
        <div class="row justify-content-center">
            @php
                $sortedCalons = $calons->sortByDesc('suaras_count');
                $maxVotes = $sortedCalons->first()->suaras_count ?? 0;
            @endphp

            @foreach($sortedCalons as $index => $calon)
                @php
                    $percentage = $totalVotes > 0 ? ($calon->suaras_count / $totalVotes) * 100 : 0;
                    $isWinner = $index === 0 && $calon->suaras_count > 0;
                @endphp

                <div class="col-lg-6 col-md-8 mb-4">
                    <div class="card {{ $isWinner ? 'border-warning' : '' }} h-100">
                        @if($isWinner)
                            <div class="card-header bg-warning text-dark text-center">
                                <i class="bi bi-trophy-fill"></i> <strong>PEMENANG SEMENTARA</strong>
                            </div>
                        @endif
                        
                        <div class="card-body">
                            <div class="row align-items-center">
                                <!-- Nomor Urut -->
                                <div class="col-3 text-center">
                                    <div class="bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center" 
                                         style="width: 80px; height: 80px; font-size: 2rem; font-weight: bold;">
                                        {{ $calon->nomor_urut }}
                                    </div>
                                </div>

                                <!-- Info Calon -->
                                <div class="col-9">
                                    <h5 class="card-title mb-2">{{ $calon->nama }}</h5>
                                    
                                    <!-- Progress Bar -->
                                    <div class="mb-2">
                                        <div class="progress" style="height: 25px;">
                                            <div class="progress-bar {{ $isWinner ? 'bg-warning' : 'bg-primary' }}" 
                                                 role="progressbar" 
                                                 style="width: {{ $percentage }}%"
                                                 aria-valuenow="{{ $percentage }}" 
                                                 aria-valuemin="0" 
                                                 aria-valuemax="100">
                                                <strong>{{ number_format($percentage, 1) }}%</strong>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Vote Count -->
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span class="text-muted">
                                            <i class="bi bi-people"></i> {{ $calon->suaras_count }} suara
                                        </span>
                                        @if($isWinner && $calon->suaras_count > 0)
                                            <span class="badge bg-warning text-dark">
                                                <i class="bi bi-trophy"></i> Terdepan
                                            </span>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            @endforeach
        </div>

        <!-- Statistics -->
        <div class="row mt-5">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-graph-up"></i> Statistik Pemilihan
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-md-3">
                                <div class="border-end">
                                    <h4 class="text-primary">{{ $calons->count() }}</h4>
                                    <p class="text-muted mb-0">Total Calon</p>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="border-end">
                                    <h4 class="text-success">{{ $totalVotes }}</h4>
                                    <p class="text-muted mb-0">Suara Masuk</p>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="border-end">
                                    <h4 class="text-info">{{ \App\Models\Pemilih::count() }}</h4>
                                    <p class="text-muted mb-0">Total Pemilih</p>
                                </div>
                            </div>
                            <div class="col-md-3">
                                @php
                                    $turnout = \App\Models\Pemilih::count() > 0 ? ($totalVotes / \App\Models\Pemilih::count()) * 100 : 0;
                                @endphp
                                <h4 class="text-warning">{{ number_format($turnout, 1) }}%</h4>
                                <p class="text-muted mb-0">Partisipasi</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="row justify-content-center mt-5">
            <div class="col-md-6 text-center">
                @auth
                    @if(Auth::user()->pemilih && !Auth::user()->pemilih->hasVoted())
                        <a href="{{ route('voting') }}" class="btn btn-success btn-lg me-3">
                            <i class="bi bi-check-square"></i> Berikan Suara
                        </a>
                    @endif
                @else
                    <a href="{{ route('login') }}" class="btn btn-primary btn-lg me-3">
                        <i class="bi bi-box-arrow-in-right"></i> Login untuk Voting
                    </a>
                @endauth
                
                <a href="{{ route('home') }}" class="btn btn-outline-secondary btn-lg">
                    <i class="bi bi-house"></i> Kembali ke Beranda
                </a>
            </div>
        </div>

        <!-- Auto Refresh Notice -->
        <div class="row justify-content-center mt-4">
            <div class="col-md-6 text-center">
                <small class="text-muted">
                    <i class="bi bi-arrow-clockwise"></i>
                    Halaman akan refresh otomatis setiap 30 detik untuk menampilkan hasil terbaru
                </small>
            </div>
        </div>
    </div>
</section>
@endsection

@section('scripts')
<script>
// Auto refresh every 30 seconds
setTimeout(function() {
    location.reload();
}, 30000);

// Add animation to progress bars
document.addEventListener('DOMContentLoaded', function() {
    const progressBars = document.querySelectorAll('.progress-bar');
    progressBars.forEach(bar => {
        const width = bar.style.width;
        bar.style.width = '0%';
        setTimeout(() => {
            bar.style.transition = 'width 1s ease-in-out';
            bar.style.width = width;
        }, 100);
    });
});
</script>
@endsection
