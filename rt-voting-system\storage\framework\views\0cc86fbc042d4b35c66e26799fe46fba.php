<?php $__env->startSection('title', 'Data Pemilih - Admin Panel'); ?>
<?php $__env->startSection('page-title', 'Data Pemilih'); ?>

<?php $__env->startSection('content'); ?>
<!-- Header Actions -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <p class="text-muted mb-0">Kelola data pemilih yang terdaftar dalam sistem</p>
    </div>
    <a href="<?php echo e(route('admin.pemilih.create')); ?>" class="btn btn-primary">
        <i class="bi bi-person-plus"></i> Tambah Pemilih
    </a>
</div>

<!-- Statistics -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <h4><?php echo e($pemilihs->total()); ?></h4>
                <small>Total Pemilih</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <h4><?php echo e($pemilihs->where('status_memilih', 'sudah')->count()); ?></h4>
                <small>Sudah Voting</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body text-center">
                <h4><?php echo e($pemilihs->where('status_memilih', 'belum')->count()); ?></h4>
                <small>Belum Voting</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <?php
                    $participation = $pemilihs->total() > 0 ? ($pemilihs->where('status_memilih', 'sudah')->count() / $pemilihs->total()) * 100 : 0;
                ?>
                <h4><?php echo e(number_format($participation, 1)); ?>%</h4>
                <small>Partisipasi</small>
            </div>
        </div>
    </div>
</div>

<!-- Pemilih Table -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="bi bi-people"></i> Daftar Pemilih
        </h5>
    </div>
    <div class="card-body">
        <?php if($pemilihs->count() > 0): ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>No</th>
                            <th>NIK</th>
                            <th>Nama Lengkap</th>
                            <th>Email</th>
                            <th>Jenis Kelamin</th>
                            <th>No. HP</th>
                            <th>Status Voting</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__currentLoopData = $pemilihs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $pemilih): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr>
                                <td><?php echo e($pemilihs->firstItem() + $index); ?></td>
                                <td>
                                    <small class="text-muted"><?php echo e($pemilih->nik); ?></small>
                                </td>
                                <td>
                                    <strong><?php echo e($pemilih->nama_lengkap); ?></strong>
                                    <br>
                                    <small class="text-muted">
                                        <?php echo e($pemilih->tempat_lahir); ?>, <?php echo e($pemilih->tanggal_lahir ? $pemilih->tanggal_lahir->format('d/m/Y') : '-'); ?>

                                    </small>
                                </td>
                                <td><?php echo e($pemilih->user->email); ?></td>
                                <td>
                                    <?php if($pemilih->jenis_kelamin == 'L'): ?>
                                        <span class="badge bg-primary">Laki-laki</span>
                                    <?php elseif($pemilih->jenis_kelamin == 'P'): ?>
                                        <span class="badge bg-pink">Perempuan</span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary">-</span>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo e($pemilih->no_hp ?? '-'); ?></td>
                                <td>
                                    <?php if($pemilih->status_memilih == 'sudah'): ?>
                                        <span class="badge bg-success">
                                            <i class="bi bi-check-circle"></i> Sudah Voting
                                        </span>
                                        <?php if($pemilih->waktu_memilih): ?>
                                            <br><small class="text-muted"><?php echo e($pemilih->waktu_memilih->format('d/m/Y H:i')); ?></small>
                                        <?php endif; ?>
                                    <?php else: ?>
                                        <span class="badge bg-warning">
                                            <i class="bi bi-clock"></i> Belum Voting
                                        </span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <a href="<?php echo e(route('admin.pemilih.edit', $pemilih)); ?>" 
                                           class="btn btn-outline-primary" 
                                           title="Edit">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        <button type="button" 
                                                class="btn btn-outline-danger" 
                                                title="Hapus"
                                                onclick="confirmDelete(<?php echo e($pemilih->id); ?>, '<?php echo e($pemilih->nama_lengkap); ?>')">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <div class="d-flex justify-content-center mt-4">
                <?php echo e($pemilihs->links()); ?>

            </div>
        <?php else: ?>
            <div class="text-center py-5">
                <i class="bi bi-people" style="font-size: 4rem; color: #dee2e6;"></i>
                <h5 class="mt-3 text-muted">Belum ada data pemilih</h5>
                <p class="text-muted">Klik tombol "Tambah Pemilih" untuk menambahkan data pemilih baru</p>
                <a href="<?php echo e(route('admin.pemilih.create')); ?>" class="btn btn-primary">
                    <i class="bi bi-person-plus"></i> Tambah Pemilih Pertama
                </a>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Konfirmasi Hapus</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Apakah Anda yakin ingin menghapus pemilih <strong id="pemilihName"></strong>?</p>
                <p class="text-danger">
                    <i class="bi bi-exclamation-triangle"></i>
                    <strong>Peringatan:</strong> Data yang dihapus tidak dapat dikembalikan!
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                <form id="deleteForm" method="POST" class="d-inline">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('DELETE'); ?>
                    <button type="submit" class="btn btn-danger">
                        <i class="bi bi-trash"></i> Hapus
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
function confirmDelete(id, name) {
    document.getElementById('pemilihName').textContent = name;
    document.getElementById('deleteForm').action = `/admin/pemilih/${id}`;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\pemilihan RT\rt\rt-voting-system\resources\views/admin/pemilih/index.blade.php ENDPATH**/ ?>