@extends('layout')

@section('title', 'Voting - Sistem Pemilihan Ketua RT')

@section('content')
<section class="py-5">
    <div class="container">
        <!-- Header -->
        <div class="row justify-content-center mb-5">
            <div class="col-md-8 text-center">
                <h2 class="mb-3">
                    <i class="bi bi-check-square"></i> Pilih Calon Ketua RT
                </h2>
                <p class="lead text-muted">
                    Silakan pilih salah satu calon di bawah ini. Anda hanya dapat memilih sekali.
                </p>
                <div class="alert alert-info">
                    <i class="bi bi-info-circle"></i>
                    <strong>Pemilih:</strong> {{ Auth::user()->name }} ({{ Auth::user()->pemilih->nik }})
                </div>
            </div>
        </div>

        <!-- Voting Form -->
        <form action="{{ route('vote.submit') }}" method="POST" id="votingForm">
            @csrf
            <div class="row justify-content-center">
                @foreach($calons as $calon)
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="card card-calon h-100 voting-card" data-calon-id="{{ $calon->id }}">
                            <div class="card-body text-center">
                                <!-- Nomor Urut -->
                                <div class="mb-3">
                                    <div class="bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center" 
                                         style="width: 100px; height: 100px; font-size: 3rem; font-weight: bold;">
                                        {{ $calon->nomor_urut }}
                                    </div>
                                </div>

                                <!-- Nama Calon -->
                                <h4 class="card-title mb-3">{{ $calon->nama }}</h4>

                                <!-- Visi -->
                                <div class="mb-3">
                                    <h6 class="text-primary">
                                        <i class="bi bi-eye"></i> Visi
                                    </h6>
                                    <p class="small text-muted">{{ $calon->visi }}</p>
                                </div>

                                <!-- Misi -->
                                <div class="mb-3">
                                    <h6 class="text-primary">
                                        <i class="bi bi-target"></i> Misi
                                    </h6>
                                    <p class="small text-muted">{{ $calon->misi }}</p>
                                </div>

                                <!-- Rekam Jejak -->
                                @if($calon->rekam_jejak)
                                    <div class="mb-3">
                                        <h6 class="text-primary">
                                            <i class="bi bi-award"></i> Rekam Jejak
                                        </h6>
                                        <p class="small text-muted">{{ $calon->rekam_jejak }}</p>
                                    </div>
                                @endif

                                <!-- Radio Button -->
                                <div class="form-check mt-4">
                                    <input class="form-check-input" 
                                           type="radio" 
                                           name="calon_id" 
                                           value="{{ $calon->id }}" 
                                           id="calon{{ $calon->id }}"
                                           style="transform: scale(1.5);">
                                    <label class="form-check-label fw-bold" for="calon{{ $calon->id }}">
                                        Pilih {{ $calon->nama }}
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>

            <!-- Submit Button -->
            <div class="row justify-content-center mt-5">
                <div class="col-md-6 text-center">
                    <button type="submit" class="btn btn-success btn-lg px-5 py-3" id="submitBtn" disabled>
                        <i class="bi bi-check-circle"></i> Kirim Suara
                    </button>
                    <p class="text-muted mt-3">
                        <small>
                            <i class="bi bi-exclamation-triangle"></i>
                            Pastikan pilihan Anda sudah benar. Suara tidak dapat diubah setelah dikirim.
                        </small>
                    </p>
                </div>
            </div>
        </form>
    </div>
</section>
@endsection

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const radioButtons = document.querySelectorAll('input[name="calon_id"]');
    const submitBtn = document.getElementById('submitBtn');
    const votingCards = document.querySelectorAll('.voting-card');
    const form = document.getElementById('votingForm');

    // Enable submit button when a choice is made
    radioButtons.forEach(radio => {
        radio.addEventListener('change', function() {
            submitBtn.disabled = false;
            
            // Remove selected class from all cards
            votingCards.forEach(card => {
                card.classList.remove('border-success', 'bg-light');
            });
            
            // Add selected class to chosen card
            const selectedCard = document.querySelector(`[data-calon-id="${this.value}"]`);
            selectedCard.classList.add('border-success', 'bg-light');
        });
    });

    // Make cards clickable
    votingCards.forEach(card => {
        card.addEventListener('click', function() {
            const calonId = this.dataset.calonId;
            const radio = document.getElementById(`calon${calonId}`);
            radio.checked = true;
            radio.dispatchEvent(new Event('change'));
        });
        
        // Add hover effect
        card.style.cursor = 'pointer';
    });

    // Confirmation before submit
    form.addEventListener('submit', function(e) {
        const selectedRadio = document.querySelector('input[name="calon_id"]:checked');
        const selectedName = selectedRadio.closest('.card').querySelector('.card-title').textContent;
        
        if (!confirm(`Apakah Anda yakin memilih ${selectedName}?\n\nSuara tidak dapat diubah setelah dikirim.`)) {
            e.preventDefault();
        }
    });
});
</script>
@endsection
