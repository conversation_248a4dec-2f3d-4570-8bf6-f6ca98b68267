<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Calon;
use App\Models\Konfigurasi;
use App\Models\Suara;
use Illuminate\Support\Facades\Auth;

class WebController extends Controller
{
    public function home()
    {
        $calons = Calon::active()->orderBy('nomor_urut')->get();
        $config = Konfigurasi::getActive();

        return view('home', compact('calons', 'config'));
    }

    public function login()
    {
        return view('login');
    }

    public function voting()
    {
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        $user = Auth::user();
        $pemilih = $user->pemilih;

        if (!$pemilih) {
            return redirect()->route('home')->with('error', 'Anda tidak terdaftar sebagai pemilih');
        }

        if ($pemilih->hasVoted()) {
            return redirect()->route('result')->with('info', 'Anda sudah memberikan suara');
        }

        $calons = Calon::active()->orderBy('nomor_urut')->get();
        $config = Konfigurasi::getActive();

        if (!$config || !$config->isVotingActive()) {
            return redirect()->route('home')->with('error', 'Pemilihan sedang tidak aktif');
        }

        return view('voting', compact('calons', 'config'));
    }

    public function result()
    {
        $calons = Calon::withCount('suaras')->orderBy('nomor_urut')->get();
        $totalVotes = Suara::count();

        return view('result', compact('calons', 'totalVotes'));
    }
}
